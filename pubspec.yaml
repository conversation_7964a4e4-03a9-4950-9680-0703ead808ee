name: mobile_qubli
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9

  # Navigation
  auto_route: ^7.8.4

  # HTTP & API
  dio: ^5.4.0

  # Local Storage
  shared_preferences: ^2.2.2

  # Functional Programming
  dartz: ^0.10.1

  # Code Generation
  freezed_annotation: ^2.4.1
  json_annotation: ^4.9.0

  # UI
  cupertino_icons: ^1.0.2
  flutter_svg: ^2.0.9

  connectivity_plus: ^4.0.2
  hive_flutter: ^1.1.0
  cached_network_image: ^3.3.0
  flutter_cache_manager: ^3.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  auto_route_generator: ^7.3.2

  # Testing
  mocktail: ^1.0.1
  state_notifier_test: ^0.0.1

  # Linting
  flutter_lints: ^3.0.0
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/

flutter_gen:
  output: lib/shared/