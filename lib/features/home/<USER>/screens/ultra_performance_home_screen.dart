// Ultra Performance Home Screen - Qubli Design Implementation with Responsive Utils
import 'package:auto_route/auto_route.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../shared/utils/responsive_utils.dart';
import '../../../../shared/widgets/responsive_widget.dart';
import '../../../../shared/theme/app_colors.dart';
import '../providers/home_provider.dart';

@RoutePage()
class UltraPerformanceHomeScreen extends ConsumerWidget {
  const UltraPerformanceHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ResponsiveLayout(
      mobile: _buildMobileLayout(context, ref),
      tablet: _buildTabletLayout(context, ref),
      desktop: _buildDesktopLayout(context, ref),
    );
  }

  Widget _buildMobileLayout(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.primary, // Use theme color
      body: SafeArea(
        child: Column(
          children: [
            // Header Section with Balance Card
            _buildHeader(context, ref),

            // Main Content Area
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: RefreshIndicator(
                  onRefresh: () => _refreshAllSections(ref),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const ResponsiveSpacing(size: SpacingSize.lg),

                        // Favorites Section
                        ResponsiveContainer(
                          useHorizontalPadding: true,
                          child: _buildFavoritesSection(context, ref),
                        ),

                        const ResponsiveSpacing(size: SpacingSize.xl),

                        // Promo Banner Section
                        ResponsiveContainer(
                          useHorizontalPadding: true,
                          child: _buildPromoBannerSection(context, ref),
                        ),

                        const ResponsiveSpacing(size: SpacingSize.xl),

                        // Articles Section
                        ResponsiveContainer(
                          useHorizontalPadding: true,
                          child: _buildArticlesSection(context, ref),
                        ),

                        const ResponsiveSpacing(size: SpacingSize.xl),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Column(
          children: [
            // Header Section with Balance Card
            _buildHeader(context, ref),

            // Main Content Area
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: RefreshIndicator(
                  onRefresh: () => _refreshAllSections(ref),
                  child: ResponsiveContainer(
                    useHorizontalPadding: true,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left column - Favorites and Articles
                          Expanded(
                            flex: 1,
                            child: Column(
                              children: [
                                const ResponsiveSpacing(size: SpacingSize.lg),
                                _buildFavoritesSection(context, ref),
                                const ResponsiveSpacing(size: SpacingSize.xl),
                                _buildArticlesSection(context, ref),
                              ],
                            ),
                          ),

                          const ResponsiveSpacing(size: SpacingSize.lg, direction: Axis.horizontal),

                          // Right column - Promo Banner
                          Expanded(
                            flex: 1,
                            child: Column(
                              children: [
                                const ResponsiveSpacing(size: SpacingSize.lg),
                                _buildPromoBannerSection(context, ref),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Column(
          children: [
            // Header Section with Balance Card
            _buildHeader(context, ref),

            // Main Content Area
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: RefreshIndicator(
                  onRefresh: () => _refreshAllSections(ref),
                  child: ResponsiveContainer(
                    useHorizontalPadding: true,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left column - Favorites (40%)
                          Expanded(
                            flex: 2,
                            child: Column(
                              children: [
                                const ResponsiveSpacing(size: SpacingSize.lg),
                                _buildFavoritesSection(context, ref),
                              ],
                            ),
                          ),

                          const ResponsiveSpacing(size: SpacingSize.lg, direction: Axis.horizontal),

                          // Middle column - Promo Banner (35%)
                          Expanded(
                            flex: 2,
                            child: Column(
                              children: [
                                const ResponsiveSpacing(size: SpacingSize.lg),
                                _buildPromoBannerSection(context, ref),
                              ],
                            ),
                          ),

                          const ResponsiveSpacing(size: SpacingSize.lg, direction: Axis.horizontal),

                          // Right column - Articles (25%)
                          Expanded(
                            flex: 1,
                            child: Column(
                              children: [
                                const ResponsiveSpacing(size: SpacingSize.lg),
                                _buildArticlesSection(context, ref),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _refreshAllSections(WidgetRef ref) async {
    await Future.wait([
      ref.read(individualBalanceNotifierProvider.notifier).refresh(),
      ref.read(individualFavoritesNotifierProvider.notifier).refresh(),
      ref.read(individualBannersNotifierProvider.notifier).refresh(),
    ]);
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    return ResponsiveContainer(
      useHorizontalPadding: true,
      child: Column(
        children: [
          const ResponsiveSpacing(size: SpacingSize.md),

          // Top bar with logo and notification
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const ResponsiveText(
                'Qubli Logo',
                type: TextType.h4,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              ResponsiveBuilder(
                builder: (context, dimensions) {
                  return Container(
                    padding: EdgeInsets.all(context.spacing.sm),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: _buildSvgIcon(
                      'assets/icons/icon_notification.svg',
                      color: Colors.white,
                      size: dimensions.isDesktop ? 28.0 :
                            dimensions.isTablet ? 24.0 : 20.0,
                    ),
                  );
                },
              ),
            ],
          ),

          const ResponsiveSpacing(size: SpacingSize.lg),

          // Balance Card
          _buildBalanceCard(context, ref),
        ],
      ),
    );
  }

  Widget _buildBalanceCard(BuildContext context, WidgetRef ref) {
    final balance = ref.watch(balanceDataProvider);
    final isLoading = ref.watch(balanceLoadingProvider);
    final error = ref.watch(balanceErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);

    // Show loading skeleton if no data
    if (isLoading && balance == null) {
      return _buildBalanceLoadingSkeleton(context);
    }

    // Show error state if no data
    if (error != null && balance == null) {
      return ResponsiveErrorCard(
        title: 'Unable to load balance',
        message: error,
        onRetry: () => ref.read(individualBalanceNotifierProvider.notifier).refresh(),
        backgroundColor: Colors.red[50],
        textColor: Colors.red,
      );
    }

    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Stack(
          children: [
            ResponsiveCard(
              backgroundColor: Colors.white,
              elevation: dimensions.isDesktop ? 8.0 : dimensions.isTablet ? 6.0 : 4.0,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Balance header with visibility toggle
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          _buildSvgIcon(
                            'assets/icons/icon_saldoanda.svg',
                            color: Colors.grey,
                            size: dimensions.isDesktop ? 20.0 :
                                  dimensions.isTablet ? 18.0 : 16.0,
                          ),
                          SizedBox(width: context.spacing.xs),
                          ResponsiveText(
                            'Saldo Anda',
                            type: TextType.bodySmall,
                            color: Colors.grey,
                          ),
                          if (isOffline) ...[
                            SizedBox(width: context.spacing.xs),
                            _buildOfflineChip(),
                          ],
                        ],
                      ),
                      Container(
                        padding: EdgeInsets.all(context.spacing.xs),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: _buildSvgIcon(
                          'assets/icons/icon_plus_putih.svg',
                          color: Colors.white,
                          size: dimensions.isDesktop ? 20.0 :
                                dimensions.isTablet ? 18.0 : 16.0,
                        ),
                      ),
                    ],
                  ),

                  ResponsiveSpacing(size: SpacingSize.sm),

                  // Balance amount
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: ResponsiveText(
                          balance?.isHidden == true
                              ? 'Rp.••••••••'
                              : 'Rp.${_formatAmount(balance?.amount ?? 200000)}',
                          type: dimensions.isTablet ? TextType.h3 : TextType.h4,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          ref.read(individualBalanceNotifierProvider.notifier).toggleVisibility();
                        },
                        child: Icon(
                          balance?.isHidden == true ? Icons.visibility : Icons.visibility_off,
                          color: Colors.grey,
                          size: dimensions.isTablet ? 24 : 20,
                        ),
                      ),
                    ],
                  ),

                  ResponsiveSpacing(size: SpacingSize.md),

                  // Action buttons
                  _buildActionButtons(context, dimensions),
                ],
              ),
            ),

            // Loading overlay when refreshing existing content
            if (isLoading && balance != null)
              Positioned(
                top: 8,
                right: 8,
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
  Widget _buildBalanceLoadingSkeleton(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Container(
          width: double.infinity,
          height: dimensions.isTablet ? 180 : 140,
          padding: EdgeInsets.all(context.spacing.lg),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 16,
                width: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const ResponsiveSpacing(size: SpacingSize.sm),
              Container(
                height: dimensions.isTablet ? 28 : 24,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const ResponsiveSpacing(size: SpacingSize.md),
              Row(
                children: List.generate(
                  4,
                  (index) => Expanded(
                    child: Container(
                      height: 50,
                      margin: EdgeInsets.only(right: index < 3 ? context.spacing.xs : 0),
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButtons(BuildContext context, ResponsiveDimensions dimensions) {
    final actions = [
      {'iconPath': 'assets/icons/icon_transfer.svg', 'label': 'Transfer'},
      {'iconPath': 'assets/icons/icon_tariktunai.svg', 'label': 'Tarik Tunai'},
      {'iconPath': 'assets/icons/icon_voucher_beranda.svg', 'label': 'Voucher'},
      {'iconPath': 'assets/icons/icon_qrcode_beranda.svg', 'label': 'QR Code'},
    ];

    // Show fewer actions on smaller screens
    final visibleActions = dimensions.isMobileXS ? actions.take(3).toList() :
                          dimensions.isMobile ? actions :
                          actions; // Show all on tablet and desktop

    final spacing = dimensions.isDesktop ? context.spacing.md :
                   dimensions.isTablet ? context.spacing.sm :
                   context.spacing.xs;

    return Row(
      children: visibleActions.asMap().entries.map((entry) {
        final index = entry.key;
        final action = entry.value;

        return Expanded(
          child: Padding(
            padding: EdgeInsets.only(
              right: index < visibleActions.length - 1 ? spacing : 0,
            ),
            child: _buildActionButton(
              context,
              action['iconPath'] as String,
              action['label'] as String,
              AppColors.primary,
              dimensions,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildActionButton(BuildContext context, String iconPath, String label, Color color, ResponsiveDimensions dimensions) {
    return GestureDetector(
      onTap: () => _handleActionTap(label),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: dimensions.isDesktop ? context.spacing.md :
                   dimensions.isTablet ? context.spacing.sm :
                   context.spacing.xs,
          horizontal: context.spacing.xs,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(
            dimensions.isDesktop ? 12.0 : 8.0,
          ),
        ),
        child: Column(
          children: [
            _buildSvgIcon(
              iconPath,
              color: color,
              size: dimensions.isDesktop ? 32.0 :
                    dimensions.isTablet ? 28.0 :
                    24.0,
            ),
            SizedBox(height: context.spacing.xs),
            ResponsiveText(
              label,
              type: dimensions.isDesktop ? TextType.bodySmall :
                    dimensions.isTablet ? TextType.bodySmall :
                    TextType.caption,
              color: Colors.black87,
              textAlign: TextAlign.center,
              maxLines: dimensions.isMobileXS ? 1 : 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSvgIcon(String iconPath, {required Color color, required double size}) {
    return SvgPicture.asset(
      iconPath,
      width: size,
      height: size,
      colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
      placeholderBuilder: (context) => Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Icon(
          Icons.image,
          size: size * 0.6,
          color: Colors.grey[500],
        ),
      ),
    );
  }

  void _handleActionTap(String action) {
    print('Action tapped: $action');
    // Add navigation logic here based on action
  }

  Widget _buildFavoritesSection(BuildContext context, WidgetRef ref) {
    final favorites = ref.watch(favoritesDataProvider);
    final isLoading = ref.watch(favoritesLoadingProvider);
    final error = ref.watch(favoritesErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);
    final pendingSyncCount = ref.watch(favoritesPendingSyncProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with status indicators
        Row(
          children: [
            const ResponsiveText(
              'Favorit',
              type: TextType.h4,
              fontWeight: FontWeight.w600,
            ),
            const Spacer(),
            if (isOffline) _buildIndicator('Offline', Colors.orange),
            if (pendingSyncCount > 0) ...[
              SizedBox(width: context.spacing.xs),
              _buildIndicator('$pendingSyncCount sync', Colors.blue),
            ],
            if (error != null) ...[
              SizedBox(width: context.spacing.xs),
              _buildErrorChip(),
            ],
            SizedBox(width: context.spacing.xs),
            GestureDetector(
              onTap: isLoading ? null : () {
                ref.read(individualFavoritesNotifierProvider.notifier).refresh();
              },
              child: ResponsiveBuilder(
                builder: (context, dimensions) {
                  return Icon(
                    Icons.refresh,
                    color: isLoading ? Colors.grey : AppColors.primary,
                    size: dimensions.isTablet ? 24 : 20,
                  );
                },
              ),
            ),
          ],
        ),

        const ResponsiveSpacing(size: SpacingSize.md),

        // Content based on individual state
        if (isLoading && favorites.isEmpty)
          _buildFavoritesLoadingSkeleton(context)
        else if (error != null && favorites.isEmpty)
          ResponsiveErrorCard(
            title: 'Error loading favorites',
            message: error,
            onRetry: () => ref.read(individualFavoritesNotifierProvider.notifier).refresh(),
          )
        else if (favorites.isEmpty)
          _buildEmptyFavorites(context)
        else
          _buildFavoritesGrid(context, favorites, isLoading),
      ],
    );
  }

  Widget _buildFavoritesLoadingSkeleton(BuildContext context) {
    return ResponsiveGrid(
      mobileColumns: 4,
      tabletColumns: 6,
      desktopColumns: 8,
      childAspectRatio: 0.8,
      children: List.generate(8, (index) => _buildSkeletonItem(context)),
    );
  }

  Widget _buildSkeletonItem(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final size = dimensions.isDesktop ? 60.0 :
                    dimensions.isTablet ? 50.0 : 40.0;

        return Column(
          children: [
            Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            SizedBox(height: context.spacing.xs),
            Container(
              height: 12,
              width: size + 10,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFavoritesGrid(BuildContext context, List favorites, bool isLoading) {
    return Stack(
      children: [
        ResponsiveGrid(
          mobileColumns: 4,
          tabletColumns: 6,
          desktopColumns: 8,
          childAspectRatio: 0.8,
          children: favorites.map<Widget>((favorite) {
            return ResponsiveFavoriteItem(
              favorite: favorite,
              isOffline: isLoading, // Use loading state as offline indicator for now
              onTap: () => _handleFavoriteTap(favorite),
            );
          }).toList(),
        ),

        // Loading overlay when refreshing existing content
        if (isLoading && favorites.isNotEmpty)
          Positioned(
            top: 0,
            right: 0,
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ),
      ],
    );
  }



  IconData _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'pulsa':
      case 'phone':
        return Icons.phone_android;
      case 'paket data':
      case 'data':
      case 'wifi':
        return Icons.wifi;
      case 'token pln':
      case 'pln':
      case 'electricity':
        return Icons.flash_on;
      case 'voucher':
      case 'gift':
        return Icons.card_giftcard;
      case 'gaming':
      case 'game':
        return Icons.games;
      case 'e-wallet':
      case 'wallet':
        return Icons.account_balance_wallet;
      case 'e-money':
      case 'money':
        return Icons.monetization_on;
      case 'transfer':
        return Icons.send;
      case 'payment':
        return Icons.payment;
      case 'topup':
        return Icons.add_circle;
      case 'history':
        return Icons.history;
      default:
        return Icons.apps;
    }
  }

  Color _getFavoriteColor(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'pulsa':
      case 'phone':
        return const Color(0xFFE91E63);
      case 'paket data':
      case 'data':
      case 'wifi':
        return const Color(0xFF2196F3);
      case 'token pln':
      case 'pln':
      case 'electricity':
        return const Color(0xFFFFC107);
      case 'voucher':
      case 'gift':
        return const Color(0xFF4CAF50);
      case 'gaming':
      case 'game':
        return const Color(0xFF9C27B0);
      case 'e-wallet':
      case 'wallet':
        return const Color(0xFF3F51B5);
      case 'e-money':
      case 'money':
        return const Color(0xFFFF9800);
      default:
        return const Color(0xFF607D8B);
    }
  }



  Widget _buildEmptyFavorites(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Container(
          height: dimensions.isTablet ? 160 : 120,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite_border,
                  color: Colors.grey,
                  size: dimensions.isTablet ? 40 : 32,
                ),
                ResponsiveSpacing(size: SpacingSize.sm),
                ResponsiveText(
                  'No favorites yet',
                  type: TextType.bodyMedium,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
                ResponsiveSpacing(size: SpacingSize.xs),
                ResponsiveText(
                  'Add your frequently used services',
                  type: TextType.bodySmall,
                  color: Colors.grey,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildIndicator(String text, Color color) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.spacing.xs,
            vertical: 2,
          ),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
          ),
          child: ResponsiveText(
            text,
            type: TextType.caption,
            color: Colors.white,
          ),
        );
      },
    );
  }



  void _handleFavoriteTap(favorite) {
    // Handle favorite tap - navigate to specific service
    print('Tapped on ${favorite.name} (${favorite.serviceType})');
    // You can add navigation logic here based on favorite.serviceType
  }

  Widget _buildPromoBannerSection(BuildContext context, WidgetRef ref) {
    final banners = ref.watch(activeBannersProvider);
    final isLoading = ref.watch(bannersLoadingProvider);
    final error = ref.watch(bannersErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);
    final currentIndex = ref.watch(currentBannerIndexProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Promo Spesial untuk Kamu!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Row(
                children: [
                  if (isOffline) _buildOfflineChip(),
                  if (error != null) ...[
                    const SizedBox(width: 8),
                    _buildErrorChip(),
                  ],
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      ref.read(individualBannersNotifierProvider.notifier).refresh();
                    },
                    child: Icon(
                      Icons.refresh,
                      color: isLoading ? Colors.grey : const Color(0xFF00BCD4),
                      size: 20,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          if (isLoading && banners.isEmpty)
            _buildBannerLoadingSkeleton()
          else if (error != null && banners.isEmpty)
            _buildBannerError(context, ref, error)
          else if (banners.isEmpty)
            _buildEmptyBanner(context)
          else
            _buildPromoBannerCarousel(banners, currentIndex, ref),
        ],
      ),
    );
  }

  Widget _buildBannerLoadingSkeleton() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  Widget _buildPromoBannerCarousel(List banners, int currentIndex, WidgetRef ref) {
    return Column(
      children: [
        SizedBox(
          height: 120,
          child: PageView.builder(
            controller: PageController(initialPage: currentIndex),
            onPageChanged: (index) {
              ref.read(individualBannersNotifierProvider.notifier).goToBanner(index);
            },
            itemCount: banners.length,
            itemBuilder: (context, index) {
              return _buildPromoBannerItem(banners[index], ref);
            },
          ),
        ),

        // Indicators
        if (banners.length > 1) ...[
          const SizedBox(height: 12),
          _buildBannerIndicators(banners.length, currentIndex, ref),
        ],
      ],
    );
  }

  Widget _buildPromoBannerItem(banner, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        ref.read(individualBannersNotifierProvider.notifier).onBannerTapped(banner);
        _handleBannerTap(banner);
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _getBannerGradientColors(banner.backgroundColor),
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // Banner image
            if (banner.imageUrl.isNotEmpty)
              Positioned(
                right: 16,
                top: 16,
                bottom: 16,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    banner.imageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildBannerPlaceholder();
                    },
                  ),
                ),
              )
            else
              Positioned(
                right: 16,
                top: 16,
                bottom: 16,
                child: _buildBannerPlaceholder(),
              ),

            // Banner content
            Positioned(
              left: 16,
              top: 16,
              bottom: 16,
              right: 100,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    banner.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    banner.description ?? 'Dapatkan promo menarik untuk berbagai layanan digital!',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      height: 1.3,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBannerPlaceholder() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(40),
      ),
      child: const Icon(
        Icons.campaign,
        color: Colors.white,
        size: 40,
      ),
    );
  }

  List<Color> _getBannerGradientColors(String? backgroundColor) {
    // Parse banner background color or use default
    switch (backgroundColor?.toLowerCase()) {
      case 'blue':
        return [const Color(0xFF2196F3), const Color(0xFF21CBF3)];
      case 'green':
        return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)];
      case 'purple':
        return [const Color(0xFF9C27B0), const Color(0xFFE91E63)];
      case 'orange':
        return [const Color(0xFFFF9800), const Color(0xFFFFB74D)];
      default:
        return [const Color(0xFF2196F3), const Color(0xFF21CBF3)];
    }
  }

  Widget _buildBannerIndicators(int count, int currentIndex, WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(count, (index) {
        return GestureDetector(
          onTap: () {
            ref.read(individualBannersNotifierProvider.notifier).goToBanner(index);
          },
          child: Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index == currentIndex
                  ? const Color(0xFF00BCD4)
                  : const Color(0xFF00BCD4).withValues(alpha: 0.3),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildBannerError(BuildContext context, WidgetRef ref, String error) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            const Text(
              'Error loading promos',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              error,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                ref.read(individualBannersNotifierProvider.notifier).refresh();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00BCD4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyBanner(BuildContext context) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.campaign, color: Colors.grey, size: 32),
            const SizedBox(height: 8),
            const Text(
              'No promos available',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Check back later for exciting offers',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _handleBannerTap(banner) {
    // Handle banner tap - navigate to promo details or external URL
    print('Tapped on banner: ${banner.title}');
    if (banner.actionUrl != null && banner.actionUrl.isNotEmpty) {
      // Navigate to URL or specific screen
      print('Navigate to: ${banner.actionUrl}');
    }
  }

  Widget _buildArticlesSection(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Artikel',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          _buildArticlesList(),
        ],
      ),
    );
  }

  Widget _buildArticlesList() {
    return Column(
      children: [
        _buildArticleItem(
          'Teknologi Mengubah Dunia Bisnis',
          'Otomatisasi, AI, dan cloud mengoptimalkan operasional. De...',
          'assets/images/article1.jpg',
        ),
        const SizedBox(height: 12),
        _buildArticleItem(
          'Teknologi Mengubah Dunia Bisnis',
          'Otomatisasi, AI, dan cloud mengoptimalkan operasional. De...',
          'assets/images/article2.jpg',
        ),
      ],
    );
  }

  Widget _buildArticleItem(String title, String description, String imagePath) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.asset(
              imagePath,
              width: 80,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 80,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.article,
                    color: Colors.grey,
                    size: 24,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        'Offline',
        style: TextStyle(
          color: Colors.white,
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildErrorChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.error_outline,
        color: Colors.white,
        size: 12,
      ),
    );
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]}.',
    );
  }
}

// ========== RESPONSIVE FAVORITE ITEM ==========
class ResponsiveFavoriteItem extends StatelessWidget {
  final dynamic favorite;
  final bool isOffline;
  final VoidCallback onTap;

  const ResponsiveFavoriteItem({
    super.key,
    required this.favorite,
    required this.isOffline,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(context.spacing.sm),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Stack(
                  children: [
                    Container(
                      width: dimensions.isTablet ? 40 : 32,
                      height: dimensions.isTablet ? 40 : 32,
                      decoration: BoxDecoration(
                        color: _getFavoriteColor(favorite.iconName).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _buildIcon(dimensions),
                      ),
                    ),

                    // Status indicators
                    if (isOffline)
                      Positioned(
                        top: -2,
                        right: -2,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.orange,
                            shape: BoxShape.circle,
                          ),
                        ),
                      )
                    else if (favorite.iconUrl != null && favorite.iconUrl!.isNotEmpty)
                      Positioned(
                        top: -2,
                        right: -2,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                  ],
                ),

                SizedBox(height: context.spacing.xs),

                ResponsiveText(
                  favorite.name,
                  type: dimensions.isTablet ? TextType.bodySmall : TextType.caption,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildIcon(ResponsiveDimensions dimensions) {
    final iconSize = dimensions.isDesktop ? 24.0 :
                    dimensions.isTablet ? 20.0 : 16.0;

    // Priority based on online/offline status:
    // Online: iconUrl > exact SVG icon > fallback icon
    // Offline: exact SVG icon > cached iconUrl > fallback icon

    if (!isOffline && favorite.iconUrl != null && favorite.iconUrl!.isNotEmpty) {
      // Online mode: Prefer API iconUrl for most up-to-date icons
      return _buildNetworkIcon(favorite.iconUrl!, iconSize);
    } else {
      // Offline mode or no iconUrl: Use local SVG icons
      final svgPath = _getExactSvgIcon(favorite.iconName);
      if (svgPath != null) {
        return _buildSvgIcon(svgPath, iconSize, dimensions);
      } else if (favorite.iconUrl != null && favorite.iconUrl!.isNotEmpty) {
        // Try cached network icon as fallback
        return _buildNetworkIcon(favorite.iconUrl!, iconSize);
      } else {
        // Final fallback to Material Icons
        return _buildFallbackIcon(favorite.iconName, iconSize);
      }
    }
  }

  Widget _buildNetworkIcon(String iconUrl, double size) {
    return CachedNetworkImage(
      imageUrl: iconUrl,
      width: size,
      height: size,
      fit: BoxFit.contain,
      placeholder: (context, url) => SizedBox(
        width: size,
        height: size,
        child: Center(
          child: SizedBox(
            width: size * 0.6,
            height: size * 0.6,
            child: CircularProgressIndicator(
              strokeWidth: 1.5,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getFavoriteColor(favorite.iconName).withValues(alpha: 0.7),
              ),
            ),
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        // Online mode: Try local SVG as fallback
        if (!isOffline) {
          final svgPath = _getExactSvgIcon(favorite.iconName);
          if (svgPath != null) {
            return _buildSvgIcon(svgPath, size, ResponsiveUtils.getDimensions(context));
          }
        }
        // Final fallback to Material Icon
        return _buildFallbackIcon(favorite.iconName, size);
      },
      // Cache configuration for offline support
      cacheManager: DefaultCacheManager(),
      memCacheWidth: (size * 2).round(), // 2x for high DPI
      memCacheHeight: (size * 2).round(),
    );
  }

  String? _getExactSvgIcon(String? iconName) {
    switch (iconName?.toLowerCase()) {
      case 'pulsa':
      case 'phone':
        return 'assets/icons/icon_pulsa.svg';
      case 'paket data':
      case 'data':
      case 'wifi':
      case 'internet':
        return 'assets/icons/icon_paketdata.svg';
      case 'token pln':
      case 'pln':
      case 'electricity':
      case 'listrik':
        return 'assets/icons/icon_tokenpln.svg';
      case 'voucher':
      case 'gift':
        return 'assets/icons/icon_voucher.svg';
      case 'gaming':
      case 'game':
        return 'assets/icons/icon_gaming.svg';
      case 'e-wallet':
      case 'wallet':
      case 'ewallet':
        return 'assets/icons/icon_ewallet.svg';
      case 'e-money':
      case 'money':
      case 'emoney':
        return 'assets/icons/icon_emoney.svg';
      case 'transfer':
        return 'assets/icons/icon_transfer.svg';
      case 'payment':
      case 'pascabayar':
        return 'assets/icons/icon_pascabayar.svg';
      case 'topup':
      case 'top up':
        return 'assets/icons/icon_topup.svg';
      case 'history':
      case 'riwayat':
        return 'assets/icons/icon_struk.svg';
      case 'bpjs':
        return 'assets/icons/icon_bpjs.svg';
      case 'tv kabel':
      case 'tv':
        return 'assets/icons/icon_tvkabel.svg';
      case 'telkom':
        return 'assets/icons/icon_telkom.svg';
      case 'air pdam':
      case 'pdam':
        return 'assets/icons/icon_airpdam.svg';
      case 'ojek online':
      case 'ojol':
        return 'assets/icons/icon_ojekonline.svg';
      case 'lainnya':
      case 'others':
        return 'assets/icons/icon_lainnya.svg';
      default:
        return null; // Will use fallback icon
    }
  }

  Widget _buildSvgIcon(String svgPath, double size, ResponsiveDimensions dimensions) {
    return SvgPicture.asset(
      svgPath,
      width: size,
      height: size,
      colorFilter: ColorFilter.mode(
        _getFavoriteColor(favorite.iconName),
        BlendMode.srcIn,
      ),
      placeholderBuilder: (context) => Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: _getFavoriteColor(favorite.iconName).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Icon(
          _getIconData(favorite.iconName),
          size: size * 0.8,
          color: _getFavoriteColor(favorite.iconName),
        ),
      ),
    );
  }

  Widget _buildFallbackIcon(String? iconName, double size) {
    return Icon(
      _getIconData(iconName),
      size: size,
      color: _getFavoriteColor(iconName),
    );
  }

  IconData _getIconData(String? iconName) {
    switch (iconName?.toLowerCase()) {
      case 'pulsa':
      case 'phone':
        return Icons.phone_android;
      case 'paket data':
      case 'data':
      case 'wifi':
        return Icons.wifi;
      case 'token pln':
      case 'pln':
      case 'electricity':
        return Icons.flash_on;
      case 'voucher':
      case 'gift':
        return Icons.card_giftcard;
      case 'gaming':
      case 'game':
        return Icons.games;
      case 'e-wallet':
      case 'wallet':
        return Icons.account_balance_wallet;
      case 'e-money':
      case 'money':
        return Icons.monetization_on;
      case 'transfer':
        return Icons.send;
      case 'payment':
        return Icons.payment;
      case 'topup':
        return Icons.add_circle;
      case 'history':
        return Icons.history;
      default:
        return Icons.apps;
    }
  }

  Color _getFavoriteColor(String? iconName) {
    switch (iconName?.toLowerCase()) {
      case 'pulsa':
      case 'phone':
        return const Color(0xFFE91E63);
      case 'paket data':
      case 'data':
      case 'wifi':
        return const Color(0xFF2196F3);
      case 'token pln':
      case 'pln':
      case 'electricity':
        return const Color(0xFFFFC107);
      case 'voucher':
      case 'gift':
        return const Color(0xFF4CAF50);
      case 'gaming':
      case 'game':
        return const Color(0xFF9C27B0);
      case 'e-wallet':
      case 'wallet':
        return const Color(0xFF3F51B5);
      case 'e-money':
      case 'money':
        return const Color(0xFFFF9800);
      default:
        return AppColors.primary;
    }
  }
}
