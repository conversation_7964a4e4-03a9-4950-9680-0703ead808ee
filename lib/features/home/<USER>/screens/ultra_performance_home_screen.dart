// Ultra Performance Home Screen using individual providers
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/utils/responsive_utils.dart';
import '../../../../shared/widgets/responsive_widget.dart';
import '../../../../shared/theme/app_colors.dart';
import '../providers/home_provider.dart';
import '../widgets/individual/individual_balance_card.dart';
import '../widgets/individual/individual_favorites_section.dart';
import '../widgets/individual/individual_banners_section.dart';

@RoutePage()
class UltraPerformanceHomeScreen extends ConsumerWidget {
  const UltraPerformanceHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ResponsiveLayout(
      mobile: _buildMobileLayout(context, ref),
      tablet: _buildTabletLayout(context, ref),
    );
  }

  Widget _buildMobileLayout(BuildContext context, WidgetRef ref) {
    return ResponsiveContainer(
      useHorizontalPadding: false,
      child: RefreshIndicator(
        onRefresh: () async {
          // Refresh all sections independently
          await Future.wait([
            ref.read(individualBalanceNotifierProvider.notifier).refresh(),
            ref.read(individualFavoritesNotifierProvider.notifier).refresh(),
            ref.read(individualBannersNotifierProvider.notifier).refresh(),
          ]);
        },
        child: const SingleChildScrollView(
          physics: AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // Individual Balance Card
              ResponsiveContainer(
                useHorizontalPadding: true,
                child: IndividualBalanceCard(),
              ),

              ResponsiveSpacing(size: SpacingSize.lg),

              // Individual Favorites Section
              ResponsiveContainer(
                useHorizontalPadding: true,
                child: IndividualFavoritesSection(),
              ),

              ResponsiveSpacing(size: SpacingSize.lg),

              // Individual Banners Section
              ResponsiveContainer(
                useHorizontalPadding: true,
                child: IndividualBannersSection(),
              ),

              ResponsiveSpacing(size: SpacingSize.xl),

              // Status Section
              ResponsiveContainer(
                useHorizontalPadding: true,
                child: UltraPerformanceStatusSection(),
              ),

              ResponsiveSpacing(size: SpacingSize.xl),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabletLayout(BuildContext context, WidgetRef ref) {
    return ResponsiveContainer(
      useHorizontalPadding: true,
      child: RefreshIndicator(
        onRefresh: () async {
          // Refresh all sections independently
          await Future.wait([
            ref.read(individualBalanceNotifierProvider.notifier).refresh(),
            ref.read(individualFavoritesNotifierProvider.notifier).refresh(),
            ref.read(individualBannersNotifierProvider.notifier).refresh(),
          ]);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left column - Balance and Status
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    IndividualBalanceCard(),
                    const ResponsiveSpacing(size: SpacingSize.lg),
                    UltraPerformanceStatusSection(),
                  ],
                ),
              ),

              const ResponsiveSpacing(size: SpacingSize.lg, direction: Axis.horizontal),

              // Right column - Favorites and Banners
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    IndividualFavoritesSection(),
                    const ResponsiveSpacing(size: SpacingSize.lg),
                    IndividualBannersSection(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Ultra Performance Status Section
/// This widget shows overall app status using computed providers
class UltraPerformanceStatusSection extends ConsumerWidget {
  const UltraPerformanceStatusSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch computed providers for overall status
    final hasAnyData = ref.watch(homeHasAnyDataProvider);
    final isAnyLoading = ref.watch(homeIsAnyLoadingProvider);
    final hasAnyError = ref.watch(homeHasAnyErrorProvider);
    final totalPendingSync = ref.watch(homeTotalPendingSyncProvider);
    final connectivityStatus = ref.watch(connectivityStatusTextProvider);
    final lastSyncTime = ref.watch(lastSyncTimeProvider);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: 20,
                ),
                SizedBox(width: 8),
                ResponsiveText(
                  'App Status',
                  type: TextType.h4,
                  fontWeight: FontWeight.bold,
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Status indicators
            _buildStatusRow('Data Loaded', hasAnyData ? 'Yes' : 'No', hasAnyData),
            _buildStatusRow('Loading', isAnyLoading ? 'Yes' : 'No', !isAnyLoading),
            _buildStatusRow('Errors', hasAnyError ? 'Yes' : 'No', !hasAnyError),
            _buildStatusRow('Connectivity', connectivityStatus, connectivityStatus == 'Online'),
            
            if (totalPendingSync > 0)
              _buildStatusRow('Pending Sync', '$totalPendingSync items', false),
            
            if (lastSyncTime != null)
              _buildStatusRow('Last Sync', _formatTime(lastSyncTime), true),

            const SizedBox(height: 12),

            // Manual sync button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  ref.read(individualConnectivityNotifierProvider.notifier).forceSyncNow();
                },
                icon: const Icon(Icons.sync, size: 16),
                label: const Text('Force Sync'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, bool isGood) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ResponsiveText(
            label,
            type: TextType.bodySmall,
            color: Colors.grey[600],
          ),
          Row(
            children: [
              ResponsiveText(
                value,
                type: TextType.bodySmall,
                fontWeight: FontWeight.w500,
              ),
              const SizedBox(width: 4),
              Icon(
                isGood ? Icons.check_circle : Icons.warning,
                color: isGood ? Colors.green : Colors.orange,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
