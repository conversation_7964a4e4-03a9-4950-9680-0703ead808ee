// Ultra Performance Home Screen - Qubli Design Implementation
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/home_provider.dart';

@RoutePage()
class UltraPerformanceHomeScreen extends ConsumerWidget {
  const UltraPerformanceHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFF00BCD4), // Qubli teal color
      body: SafeArea(
        child: Column(
          children: [
            // Header Section with Balance Card
            _buildHeader(context, ref),

            // Main Content Area
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: RefreshIndicator(
                  onRefresh: () => _refreshAllSections(ref),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),

                        // Favorites Section
                        _buildFavoritesSection(context, ref),

                        const SizedBox(height: 24),

                        // Promo Banner Section
                        _buildPromoBannerSection(context, ref),

                        const SizedBox(height: 24),

                        // Articles Section
                        _buildArticlesSection(context, ref),

                        const SizedBox(height: 100), // Bottom padding
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _refreshAllSections(WidgetRef ref) async {
    await Future.wait([
      ref.read(individualBalanceNotifierProvider.notifier).refresh(),
      ref.read(individualFavoritesNotifierProvider.notifier).refresh(),
      ref.read(individualBannersNotifierProvider.notifier).refresh(),
    ]);
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Top bar with logo and notification
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Qubli Logo',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.notifications_outlined,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Balance Card
          _buildBalanceCard(context, ref),
        ],
      ),
    );
  }

  Widget _buildBalanceCard(BuildContext context, WidgetRef ref) {
    final balance = ref.watch(balanceDataProvider);
    final isLoading = ref.watch(balanceLoadingProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Balance header with visibility toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(Icons.account_balance_wallet, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  const Text(
                    'Saldo Anda',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  if (isOffline) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'Offline',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: const Color(0xFF00BCD4),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Balance amount
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (isLoading && balance == null)
                Container(
                  height: 24,
                  width: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(4),
                  ),
                )
              else
                Text(
                  balance?.isHidden == true
                      ? 'Rp.••••••••'
                      : 'Rp.${_formatAmount(balance?.amount ?? 200000)}',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              GestureDetector(
                onTap: () {
                  ref.read(individualBalanceNotifierProvider.notifier).toggleVisibility();
                },
                child: Icon(
                  balance?.isHidden == true ? Icons.visibility : Icons.visibility_off,
                  color: Colors.grey,
                  size: 20,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              Expanded(child: _buildActionButton(Icons.send, 'Transfer', const Color(0xFF00BCD4))),
              const SizedBox(width: 8),
              Expanded(child: _buildActionButton(Icons.account_balance, 'Tarik Tunai', const Color(0xFF00BCD4))),
              const SizedBox(width: 8),
              Expanded(child: _buildActionButton(Icons.receipt, 'Voucher', const Color(0xFF00BCD4))),
              const SizedBox(width: 8),
              Expanded(child: _buildActionButton(Icons.qr_code, 'QR Code', const Color(0xFF00BCD4))),
            ],
          ),
        ],
      ),
    );
  }
  Widget _buildActionButton(IconData icon, String label, Color color) {
    return GestureDetector(
      onTap: () {},
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesSection(BuildContext context, WidgetRef ref) {
    final favorites = ref.watch(favoritesDataProvider);
    final isLoading = ref.watch(favoritesLoadingProvider);
    final error = ref.watch(favoritesErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Favorit',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Row(
                children: [
                  if (isOffline) _buildOfflineChip(),
                  if (error != null) ...[
                    const SizedBox(width: 8),
                    _buildErrorChip(),
                  ],
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      ref.read(individualFavoritesNotifierProvider.notifier).refresh();
                    },
                    child: Icon(
                      Icons.refresh,
                      color: isLoading ? Colors.grey : const Color(0xFF00BCD4),
                      size: 20,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          if (isLoading && favorites.isEmpty)
            _buildFavoritesLoadingSkeleton()
          else if (error != null && favorites.isEmpty)
            _buildFavoritesError(context, ref, error)
          else if (favorites.isEmpty)
            _buildEmptyFavorites(context)
          else
            _buildFavoritesGrid(favorites),
        ],
      ),
    );
  }

  Widget _buildFavoritesLoadingSkeleton() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        childAspectRatio: 1,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: 8,
      itemBuilder: (context, index) {
        return Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 12,
              width: 40,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFavoritesGrid(List favorites) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        childAspectRatio: 1,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final favorite = favorites[index];
        return GestureDetector(
          onTap: () => _handleFavoriteTap(favorite),
          child: Column(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getFavoriteColor(favorite.iconName).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _buildFavoriteIcon(favorite),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                favorite.name,
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFavoriteIcon(favorite) {
    if (favorite.iconUrl.isNotEmpty) {
      return Image.network(
        favorite.iconUrl,
        width: 24,
        height: 24,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return Icon(
            _getIconData(favorite.iconName),
            color: _getFavoriteColor(favorite.iconName),
            size: 24,
          );
        },
      );
    } else {
      return Icon(
        _getIconData(favorite.iconName),
        color: _getFavoriteColor(favorite.iconName),
        size: 24,
      );
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'pulsa':
      case 'phone':
        return Icons.phone_android;
      case 'paket data':
      case 'data':
      case 'wifi':
        return Icons.wifi;
      case 'token pln':
      case 'pln':
      case 'electricity':
        return Icons.flash_on;
      case 'voucher':
      case 'gift':
        return Icons.card_giftcard;
      case 'gaming':
      case 'game':
        return Icons.games;
      case 'e-wallet':
      case 'wallet':
        return Icons.account_balance_wallet;
      case 'e-money':
      case 'money':
        return Icons.monetization_on;
      case 'transfer':
        return Icons.send;
      case 'payment':
        return Icons.payment;
      case 'topup':
        return Icons.add_circle;
      case 'history':
        return Icons.history;
      default:
        return Icons.apps;
    }
  }

  Color _getFavoriteColor(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'pulsa':
      case 'phone':
        return const Color(0xFFE91E63);
      case 'paket data':
      case 'data':
      case 'wifi':
        return const Color(0xFF2196F3);
      case 'token pln':
      case 'pln':
      case 'electricity':
        return const Color(0xFFFFC107);
      case 'voucher':
      case 'gift':
        return const Color(0xFF4CAF50);
      case 'gaming':
      case 'game':
        return const Color(0xFF9C27B0);
      case 'e-wallet':
      case 'wallet':
        return const Color(0xFF3F51B5);
      case 'e-money':
      case 'money':
        return const Color(0xFFFF9800);
      default:
        return const Color(0xFF607D8B);
    }
  }

  Widget _buildFavoritesError(BuildContext context, WidgetRef ref, String error) {
    return Container(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            const Text(
              'Error loading favorites',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              error,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                ref.read(individualFavoritesNotifierProvider.notifier).refresh();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00BCD4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyFavorites(BuildContext context) {
    return Container(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.favorite_border, color: Colors.grey, size: 32),
            const SizedBox(height: 8),
            const Text(
              'No favorites yet',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Add your frequently used services',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _handleFavoriteTap(favorite) {
    // Handle favorite tap - navigate to specific service
    print('Tapped on ${favorite.name} (${favorite.serviceType})');
    // You can add navigation logic here based on favorite.serviceType
  }

  Widget _buildPromoBannerSection(BuildContext context, WidgetRef ref) {
    final banners = ref.watch(activeBannersProvider);
    final isLoading = ref.watch(bannersLoadingProvider);
    final error = ref.watch(bannersErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);
    final currentIndex = ref.watch(currentBannerIndexProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Promo Spesial untuk Kamu!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Row(
                children: [
                  if (isOffline) _buildOfflineChip(),
                  if (error != null) ...[
                    const SizedBox(width: 8),
                    _buildErrorChip(),
                  ],
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      ref.read(individualBannersNotifierProvider.notifier).refresh();
                    },
                    child: Icon(
                      Icons.refresh,
                      color: isLoading ? Colors.grey : const Color(0xFF00BCD4),
                      size: 20,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          if (isLoading && banners.isEmpty)
            _buildBannerLoadingSkeleton()
          else if (error != null && banners.isEmpty)
            _buildBannerError(context, ref, error)
          else if (banners.isEmpty)
            _buildEmptyBanner(context)
          else
            _buildPromoBannerCarousel(banners, currentIndex, ref),
        ],
      ),
    );
  }

  Widget _buildBannerLoadingSkeleton() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  Widget _buildPromoBannerCarousel(List banners, int currentIndex, WidgetRef ref) {
    return Column(
      children: [
        SizedBox(
          height: 120,
          child: PageView.builder(
            controller: PageController(initialPage: currentIndex),
            onPageChanged: (index) {
              ref.read(individualBannersNotifierProvider.notifier).goToBanner(index);
            },
            itemCount: banners.length,
            itemBuilder: (context, index) {
              return _buildPromoBannerItem(banners[index], ref);
            },
          ),
        ),

        // Indicators
        if (banners.length > 1) ...[
          const SizedBox(height: 12),
          _buildBannerIndicators(banners.length, currentIndex, ref),
        ],
      ],
    );
  }

  Widget _buildPromoBannerItem(banner, WidgetRef ref) {
    return GestureDetector(
      onTap: () {
        ref.read(individualBannersNotifierProvider.notifier).onBannerTapped(banner);
        _handleBannerTap(banner);
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: _getBannerGradientColors(banner.backgroundColor),
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // Banner image
            if (banner.imageUrl.isNotEmpty)
              Positioned(
                right: 16,
                top: 16,
                bottom: 16,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    banner.imageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return _buildBannerPlaceholder();
                    },
                  ),
                ),
              )
            else
              Positioned(
                right: 16,
                top: 16,
                bottom: 16,
                child: _buildBannerPlaceholder(),
              ),

            // Banner content
            Positioned(
              left: 16,
              top: 16,
              bottom: 16,
              right: 100,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    banner.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    banner.description ?? 'Dapatkan promo menarik untuk berbagai layanan digital!',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      height: 1.3,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBannerPlaceholder() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(40),
      ),
      child: const Icon(
        Icons.campaign,
        color: Colors.white,
        size: 40,
      ),
    );
  }

  List<Color> _getBannerGradientColors(String? backgroundColor) {
    // Parse banner background color or use default
    switch (backgroundColor?.toLowerCase()) {
      case 'blue':
        return [const Color(0xFF2196F3), const Color(0xFF21CBF3)];
      case 'green':
        return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)];
      case 'purple':
        return [const Color(0xFF9C27B0), const Color(0xFFE91E63)];
      case 'orange':
        return [const Color(0xFFFF9800), const Color(0xFFFFB74D)];
      default:
        return [const Color(0xFF2196F3), const Color(0xFF21CBF3)];
    }
  }

  Widget _buildBannerIndicators(int count, int currentIndex, WidgetRef ref) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(count, (index) {
        return GestureDetector(
          onTap: () {
            ref.read(individualBannersNotifierProvider.notifier).goToBanner(index);
          },
          child: Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index == currentIndex
                  ? const Color(0xFF00BCD4)
                  : const Color(0xFF00BCD4).withValues(alpha: 0.3),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildBannerError(BuildContext context, WidgetRef ref, String error) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            const Text(
              'Error loading promos',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              error,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                ref.read(individualBannersNotifierProvider.notifier).refresh();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF00BCD4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyBanner(BuildContext context) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.campaign, color: Colors.grey, size: 32),
            const SizedBox(height: 8),
            const Text(
              'No promos available',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Check back later for exciting offers',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _handleBannerTap(banner) {
    // Handle banner tap - navigate to promo details or external URL
    print('Tapped on banner: ${banner.title}');
    if (banner.actionUrl != null && banner.actionUrl.isNotEmpty) {
      // Navigate to URL or specific screen
      print('Navigate to: ${banner.actionUrl}');
    }
  }

  Widget _buildArticlesSection(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Artikel',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          _buildArticlesList(),
        ],
      ),
    );
  }

  Widget _buildArticlesList() {
    return Column(
      children: [
        _buildArticleItem(
          'Teknologi Mengubah Dunia Bisnis',
          'Otomatisasi, AI, dan cloud mengoptimalkan operasional. De...',
          'assets/images/article1.jpg',
        ),
        const SizedBox(height: 12),
        _buildArticleItem(
          'Teknologi Mengubah Dunia Bisnis',
          'Otomatisasi, AI, dan cloud mengoptimalkan operasional. De...',
          'assets/images/article2.jpg',
        ),
      ],
    );
  }

  Widget _buildArticleItem(String title, String description, String imagePath) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.asset(
              imagePath,
              width: 80,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 80,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.article,
                    color: Colors.grey,
                    size: 24,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]}.',
    );
  }
}
