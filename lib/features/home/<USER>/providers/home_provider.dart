import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/balance_entity.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/favorite_entity.dart';
import '../../domain/providers/home_provider.dart';
import 'state/home_notifier.dart';
import 'state/home_state.dart';
import 'enhanced_home_notifier.dart';
import 'individual/balance_notifier.dart';
import 'individual/favorites_notifier.dart';
import 'individual/banners_notifier.dart';
import 'individual/connectivity_notifier.dart';

// ========== PRESENTATION LEVEL PROVIDERS (UI State Management) ==========

/// Main Home State Notifier Provider - Presentation layer
final homeNotifierProvider = StateNotifierProvider<HomeNotifier, HomeState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final syncManager = ref.watch(syncManagerProvider);

  return HomeNotifier(repository, networkInfo, syncManager);
});

/// Individual providers for specific UI needs
final balanceProvider = Provider<BalanceEntity?>((ref) {
  return ref.watch(homeNotifierProvider).balance;
});

final favoritesProvider = Provider<List<FavoriteEntity>>((ref) {
  return ref.watch(homeNotifierProvider).favorites;
});

final bannersProvider = Provider<List<BannerEntity>>((ref) {
  return ref.watch(homeNotifierProvider).banners;
});

final isOfflineProvider = Provider<bool>((ref) {
  return ref.watch(homeNotifierProvider).isOffline;
});

final pendingSyncCountProvider = Provider<int>((ref) {
  return ref.watch(homeNotifierProvider).pendingSyncCount;
});

// ========== ENHANCED PROVIDER (RECOMMENDED) ==========
/// Enhanced Home State Notifier Provider with modern patterns
/// This provider includes auto-refresh, better error handling, and improved performance
final enhancedHomeNotifierProvider = StateNotifierProvider<EnhancedHomeNotifier, HomeState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final syncManager = ref.watch(syncManagerProvider);

  return EnhancedHomeNotifier(repository, networkInfo, syncManager);
});

// Enhanced individual providers
final enhancedBalanceProvider = Provider<BalanceEntity?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).balance;
});

final enhancedFavoritesProvider = Provider<List<FavoriteEntity>>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).favorites;
});

final enhancedBannersProvider = Provider<List<BannerEntity>>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).banners;
});

final enhancedIsOfflineProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isOffline;
});

final enhancedPendingSyncCountProvider = Provider<int>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).pendingSyncCount;
});

// Enhanced loading state providers
final isBalanceLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isBalanceLoading;
});

final isFavoritesLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isFavoritesLoading;
});

final isBannersLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isBannersLoading;
});

// Enhanced error state providers
final balanceErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).balanceError;
});

final favoritesErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).favoritesError;
});

final bannersErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).bannersError;
});

// ========== INDIVIDUAL PROVIDERS (BEST PERFORMANCE) ==========
/// These providers offer the best performance by managing state independently
/// Each section only rebuilds when its specific data changes

/// Individual Balance Provider
final individualBalanceNotifierProvider = StateNotifierProvider<BalanceNotifier, BalanceState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  return BalanceNotifier(repository);
});

/// Individual Favorites Provider
final individualFavoritesNotifierProvider = StateNotifierProvider<FavoritesNotifier, FavoritesState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  return FavoritesNotifier(repository);
});

/// Individual Banners Provider
final individualBannersNotifierProvider = StateNotifierProvider<BannersNotifier, BannersState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  return BannersNotifier(repository);
});

/// Individual Connectivity Provider (Simplified Demo Version)
final individualConnectivityNotifierProvider = StateNotifierProvider<ConnectivityNotifier, ConnectivityState>((ref) {
  return ConnectivityNotifier();
});

// ========== GRANULAR ACCESS PROVIDERS ==========
/// These providers give access to specific pieces of data for maximum performance

// Balance specific providers
final balanceDataProvider = Provider<BalanceEntity?>((ref) {
  return ref.watch(individualBalanceNotifierProvider).balance;
});

final balanceLoadingProvider = Provider<bool>((ref) {
  return ref.watch(individualBalanceNotifierProvider).isLoading;
});

final balanceErrorStateProvider = Provider<String?>((ref) {
  return ref.watch(individualBalanceNotifierProvider).error;
});

final balanceHasDataProvider = Provider<bool>((ref) {
  return ref.watch(individualBalanceNotifierProvider).hasData;
});

// Favorites specific providers
final favoritesDataProvider = Provider<List<FavoriteEntity>>((ref) {
  return ref.watch(individualFavoritesNotifierProvider).favorites;
});

final favoritesLoadingProvider = Provider<bool>((ref) {
  return ref.watch(individualFavoritesNotifierProvider).isLoading;
});

final favoritesErrorStateProvider = Provider<String?>((ref) {
  return ref.watch(individualFavoritesNotifierProvider).error;
});

final favoritesHasDataProvider = Provider<bool>((ref) {
  return ref.watch(individualFavoritesNotifierProvider).hasData;
});

final favoritesPendingSyncProvider = Provider<int>((ref) {
  return ref.watch(individualFavoritesNotifierProvider).pendingSyncCount;
});

// Banners specific providers
final bannersDataProvider = Provider<List<BannerEntity>>((ref) {
  return ref.watch(individualBannersNotifierProvider).banners;
});

final bannersLoadingProvider = Provider<bool>((ref) {
  return ref.watch(individualBannersNotifierProvider).isLoading;
});

final bannersErrorStateProvider = Provider<String?>((ref) {
  return ref.watch(individualBannersNotifierProvider).error;
});

final activeBannersProvider = Provider<List<BannerEntity>>((ref) {
  return ref.watch(individualBannersNotifierProvider).activeBanners;
});

final currentBannerProvider = Provider<BannerEntity?>((ref) {
  return ref.watch(individualBannersNotifierProvider).currentBanner;
});

final currentBannerIndexProvider = Provider<int>((ref) {
  return ref.watch(individualBannersNotifierProvider).currentIndex;
});

// Connectivity specific providers
final connectivityStatusProvider = Provider<bool>((ref) {
  return ref.watch(individualConnectivityNotifierProvider).isOnline;
});

final isOfflineStatusProvider = Provider<bool>((ref) {
  return ref.watch(individualConnectivityNotifierProvider).isOffline;
});

final connectivityPendingSyncProvider = Provider<int>((ref) {
  return ref.watch(individualConnectivityNotifierProvider).pendingSyncCount;
});

final connectivityStatusTextProvider = Provider<String>((ref) {
  return ref.watch(individualConnectivityNotifierProvider).statusText;
});

final lastSyncTimeProvider = Provider<DateTime?>((ref) {
  return ref.watch(individualConnectivityNotifierProvider).lastSyncTime;
});

// ========== COMPUTED PROVIDERS ==========
/// These providers compute derived state from multiple individual providers

final homeHasAnyDataProvider = Provider<bool>((ref) {
  final hasBalance = ref.watch(balanceHasDataProvider);
  final hasFavorites = ref.watch(favoritesHasDataProvider);
  final hasBanners = ref.watch(activeBannersProvider).isNotEmpty;
  return hasBalance || hasFavorites || hasBanners;
});

final homeIsAnyLoadingProvider = Provider<bool>((ref) {
  final balanceLoading = ref.watch(balanceLoadingProvider);
  final favoritesLoading = ref.watch(favoritesLoadingProvider);
  final bannersLoading = ref.watch(bannersLoadingProvider);
  return balanceLoading || favoritesLoading || bannersLoading;
});

final homeHasAnyErrorProvider = Provider<bool>((ref) {
  final balanceError = ref.watch(balanceErrorStateProvider);
  final favoritesError = ref.watch(favoritesErrorStateProvider);
  final bannersError = ref.watch(bannersErrorStateProvider);
  return balanceError != null || favoritesError != null || bannersError != null;
});

final homeTotalPendingSyncProvider = Provider<int>((ref) {
  final favoritesSync = ref.watch(favoritesPendingSyncProvider);
  final connectivitySync = ref.watch(connectivityPendingSyncProvider);
  return favoritesSync + connectivitySync;
});