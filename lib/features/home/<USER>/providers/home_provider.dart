import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/balance_entity.dart';
import '../../domain/entities/banner_entity.dart';
import '../../domain/entities/favorite_entity.dart';
import '../../domain/providers/home_provider.dart';
import 'state/home_notifier.dart';
import 'state/home_state.dart';
import 'enhanced_home_notifier.dart';
import 'individual/balance_notifier.dart';
import 'individual/favorites_notifier.dart';
import 'individual/banners_notifier.dart';
import 'individual/connectivity_notifier.dart';

// ========== PRESENTATION LEVEL PROVIDERS (UI State Management) ==========

/// Main Home State Notifier Provider - Presentation layer
final homeNotifierProvider = StateNotifierProvider<HomeNotifier, HomeState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final syncManager = ref.watch(syncManagerProvider);

  return HomeNotifier(repository, networkInfo, syncManager);
});

/// Individual providers for specific UI needs
final balanceProvider = Provider<BalanceEntity?>((ref) {
  return ref.watch(homeNotifierProvider).balance;
});

final favoritesProvider = Provider<List<FavoriteEntity>>((ref) {
  return ref.watch(homeNotifierProvider).favorites;
});

final bannersProvider = Provider<List<BannerEntity>>((ref) {
  return ref.watch(homeNotifierProvider).banners;
});

final isOfflineProvider = Provider<bool>((ref) {
  return ref.watch(homeNotifierProvider).isOffline;
});

final pendingSyncCountProvider = Provider<int>((ref) {
  return ref.watch(homeNotifierProvider).pendingSyncCount;
});

// ========== ENHANCED PROVIDER (RECOMMENDED) ==========
/// Enhanced Home State Notifier Provider with modern patterns
/// This provider includes auto-refresh, better error handling, and improved performance
final enhancedHomeNotifierProvider = StateNotifierProvider<EnhancedHomeNotifier, HomeState>((ref) {
  final repository = ref.watch(homeRepositoryProvider);
  final networkInfo = ref.watch(networkInfoProvider);
  final syncManager = ref.watch(syncManagerProvider);

  return EnhancedHomeNotifier(repository, networkInfo, syncManager);
});

// Enhanced individual providers
final enhancedBalanceProvider = Provider<BalanceEntity?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).balance;
});

final enhancedFavoritesProvider = Provider<List<FavoriteEntity>>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).favorites;
});

final enhancedBannersProvider = Provider<List<BannerEntity>>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).banners;
});

final enhancedIsOfflineProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isOffline;
});

final enhancedPendingSyncCountProvider = Provider<int>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).pendingSyncCount;
});

// Enhanced loading state providers
final isBalanceLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isBalanceLoading;
});

final isFavoritesLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isFavoritesLoading;
});

final isBannersLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).isBannersLoading;
});

// Enhanced error state providers
final balanceErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).balanceError;
});

final favoritesErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).favoritesError;
});

final bannersErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedHomeNotifierProvider).bannersError;
});