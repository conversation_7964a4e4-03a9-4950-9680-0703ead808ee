import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../domain/entities/banner_entity.dart';
import '../../../data/repositories/home_repository.dart';

/// Individual Banners State
class BannersState {
  final List<BannerEntity> banners;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;
  final int currentIndex;

  const BannersState({
    this.banners = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
    this.currentIndex = 0,
  });

  BannersState copyWith({
    List<BannerEntity>? banners,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
    int? currentIndex,
  }) {
    return BannersState(
      banners: banners ?? this.banners,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      currentIndex: currentIndex ?? this.currentIndex,
    );
  }

  bool get hasData => banners.isNotEmpty;
  bool get hasError => error != null;
  bool get isStale => lastUpdated == null || 
      DateTime.now().difference(lastUpdated!).inMinutes > 15;
  
  List<BannerEntity> get activeBanners => banners.where((banner) => 
      banner.isActive && (banner.validUntil == null || 
      banner.validUntil!.isAfter(DateTime.now()))).toList();
  
  BannerEntity? get currentBanner => activeBanners.isNotEmpty && 
      currentIndex < activeBanners.length ? activeBanners[currentIndex] : null;
}

/// Individual Banners Notifier
class BannersNotifier extends StateNotifier<BannersState> {
  final HomeRepository _repository;
  Timer? _autoRefreshTimer;
  Timer? _carouselTimer;

  BannersNotifier(this._repository) : super(const BannersState()) {
    _setupAutoRefresh();
    _setupCarousel();
    loadBanners();
  }

  void _setupAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(const Duration(minutes: 15), (_) {
      if (mounted) {
        loadBanners(forceRefresh: true);
      }
    });
  }

  void _setupCarousel() {
    _carouselTimer?.cancel();
    _carouselTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      if (mounted && state.activeBanners.isNotEmpty) {
        nextBanner();
      }
    });
  }

  Future<void> loadBanners({bool forceRefresh = false}) async {
    if (!forceRefresh && state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
    );

    try {
      final result = await _repository.getBanners();

      if (mounted) {
        result.fold(
          (error) => state = state.copyWith(
            isLoading: false,
            error: error.message,
          ),
          (banners) {
            // Sort banners by sortOrder
            final sortedBanners = List<BannerEntity>.from(banners)
              ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
            
            state = state.copyWith(
              banners: sortedBanners,
              isLoading: false,
              error: null,
              lastUpdated: DateTime.now(),
              currentIndex: 0, // Reset to first banner
            );
          },
        );
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          error: e.toString(),
        );
      }
    }
  }

  void nextBanner() {
    if (state.activeBanners.isEmpty) return;
    
    final nextIndex = (state.currentIndex + 1) % state.activeBanners.length;
    state = state.copyWith(currentIndex: nextIndex);
  }

  void previousBanner() {
    if (state.activeBanners.isEmpty) return;
    
    final prevIndex = state.currentIndex > 0 
        ? state.currentIndex - 1 
        : state.activeBanners.length - 1;
    state = state.copyWith(currentIndex: prevIndex);
  }

  void goToBanner(int index) {
    if (index >= 0 && index < state.activeBanners.length) {
      state = state.copyWith(currentIndex: index);
    }
  }

  void pauseCarousel() {
    _carouselTimer?.cancel();
  }

  void resumeCarousel() {
    _setupCarousel();
  }

  Future<void> onBannerTapped(BannerEntity banner) async {
    // Handle banner tap - could navigate, track analytics, etc.
    try {
      // Example: Track banner interaction
      // await _analyticsService.trackBannerTap(banner.id);
      
      // Example: Navigate to action URL
      // if (banner.actionUrl != null) {
      //   await _navigationService.navigateToUrl(banner.actionUrl!);
      // }
    } catch (e) {
      // Handle error silently or show user feedback
      if (mounted) {
        state = state.copyWith(error: 'Failed to handle banner action');
      }
    }
  }

  Future<void> refresh() async {
    await loadBanners(forceRefresh: true);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  @override
  void dispose() {
    _autoRefreshTimer?.cancel();
    _carouselTimer?.cancel();
    super.dispose();
  }
}
