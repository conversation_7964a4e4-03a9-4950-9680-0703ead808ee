import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../domain/entities/balance_entity.dart';
import '../../../data/repositories/home_repository.dart';

/// Individual Balance State
class BalanceState {
  final BalanceEntity? balance;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const BalanceState({
    this.balance,
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  BalanceState copyWith({
    BalanceEntity? balance,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return BalanceState(
      balance: balance ?? this.balance,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get hasData => balance != null;
  bool get hasError => error != null;
  bool get isStale => lastUpdated == null || 
      DateTime.now().difference(lastUpdated!).inMinutes > 15;
}

/// Individual Balance Notifier
class BalanceNotifier extends StateNotifier<BalanceState> {
  final HomeRepository _repository;
  Timer? _autoRefreshTimer;

  BalanceNotifier(this._repository) : super(const BalanceState()) {
    _setupAutoRefresh();
    loadBalance();
  }

  void _setupAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(const Duration(minutes: 15), (_) {
      if (mounted) {
        loadBalance(forceRefresh: true);
      }
    });
  }

  Future<void> loadBalance({bool forceRefresh = false}) async {
    if (!forceRefresh && state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
    );

    try {
      final result = await _repository.getBalance();

      if (mounted) {
        result.fold(
          (error) => state = state.copyWith(
            isLoading: false,
            error: error.message,
          ),
          (balance) => state = state.copyWith(
            balance: balance,
            isLoading: false,
            error: null,
            lastUpdated: DateTime.now(),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          error: e.toString(),
        );
      }
    }
  }

  Future<void> toggleVisibility() async {
    if (state.balance == null) return;

    // Optimistic update
    final newBalance = state.balance!.copyWith(
      isHidden: !state.balance!.isHidden,
    );
    state = state.copyWith(balance: newBalance);

    try {
      final result = await _repository.updateBalanceVisibility(newBalance.isHidden);

      if (mounted) {
        result.fold(
          (error) {
            // Revert on error
            final revertedBalance = state.balance!.copyWith(
              isHidden: !state.balance!.isHidden,
            );
            state = state.copyWith(
              balance: revertedBalance,
              error: error.message,
            );
          },
          (_) => state = state.copyWith(error: null),
        );
      }
    } catch (e) {
      if (mounted) {
        // Revert on error
        final revertedBalance = state.balance!.copyWith(
          isHidden: !state.balance!.isHidden,
        );
        state = state.copyWith(
          balance: revertedBalance,
          error: e.toString(),
        );
      }
    }
  }

  Future<void> refresh() async {
    await loadBalance(forceRefresh: true);
  }

  @override
  void dispose() {
    _autoRefreshTimer?.cancel();
    super.dispose();
  }
}
