import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/network/network_info.dart';
import '../../../../../core/sync/sync_manager.dart';

/// Individual Connectivity State
class ConnectivityState {
  final bool isOnline;
  final bool isConnecting;
  final int pendingSyncCount;
  final DateTime? lastSyncTime;
  final String? syncError;

  const ConnectivityState({
    this.isOnline = true,
    this.isConnecting = false,
    this.pendingSyncCount = 0,
    this.lastSyncTime,
    this.syncError,
  });

  ConnectivityState copyWith({
    bool? isOnline,
    bool? isConnecting,
    int? pendingSyncCount,
    DateTime? lastSyncTime,
    String? syncError,
  }) {
    return ConnectivityState(
      isOnline: isOnline ?? this.isOnline,
      isConnecting: isConnecting ?? this.isConnecting,
      pendingSyncCount: pendingSyncCount ?? this.pendingSyncCount,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      syncError: syncError,
    );
  }

  bool get isOffline => !isOnline;
  bool get hasPendingSync => pendingSyncCount > 0;
  bool get hasSyncError => syncError != null;
  
  String get statusText {
    if (isConnecting) return 'Connecting...';
    if (isOffline) return 'Offline';
    if (hasPendingSync) return '$pendingSyncCount pending';
    return 'Online';
  }
}

/// Individual Connectivity Notifier
class ConnectivityNotifier extends StateNotifier<ConnectivityState> {
  final NetworkInfo _networkInfo;
  final SyncManager _syncManager;
  StreamSubscription<bool>? _connectivitySubscription;
  Timer? _syncCheckTimer;

  ConnectivityNotifier(this._networkInfo, this._syncManager) 
      : super(const ConnectivityState()) {
    _initializeConnectivity();
    _setupSyncCheck();
    _updatePendingSyncCount();
  }

  void _initializeConnectivity() {
    _connectivitySubscription = _networkInfo.onConnectivityChanged.listen(
      (isConnected) async {
        state = state.copyWith(
          isOnline: isConnected,
          isConnecting: false,
        );

        if (isConnected) {
          await _handleReconnection();
        }
      },
    );

    // Check initial connectivity
    _checkInitialConnectivity();
  }

  Future<void> _checkInitialConnectivity() async {
    try {
      final isConnected = await _networkInfo.isConnected;
      state = state.copyWith(isOnline: isConnected);
    } catch (e) {
      state = state.copyWith(
        isOnline: false,
        syncError: 'Failed to check connectivity',
      );
    }
  }

  void _setupSyncCheck() {
    _syncCheckTimer?.cancel();
    _syncCheckTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      if (mounted) {
        _updatePendingSyncCount();
      }
    });
  }

  Future<void> _handleReconnection() async {
    try {
      state = state.copyWith(isConnecting: true, syncError: null);
      
      // Sync pending operations
      await _syncPendingOperations();
      
      state = state.copyWith(
        isConnecting: false,
        lastSyncTime: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        isConnecting: false,
        syncError: e.toString(),
      );
    }
  }

  Future<void> _syncPendingOperations() async {
    try {
      await _syncManager.syncPendingOperations();
      await _updatePendingSyncCount();
    } catch (e) {
      throw Exception('Sync failed: $e');
    }
  }

  Future<void> _updatePendingSyncCount() async {
    try {
      final count = await _syncManager.getPendingOperationsCount();
      if (mounted) {
        state = state.copyWith(pendingSyncCount: count);
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(syncError: 'Failed to get sync count');
      }
    }
  }

  Future<void> forceSyncNow() async {
    if (state.isOffline) {
      state = state.copyWith(syncError: 'Cannot sync while offline');
      return;
    }

    try {
      state = state.copyWith(isConnecting: true, syncError: null);
      await _syncPendingOperations();
      state = state.copyWith(
        isConnecting: false,
        lastSyncTime: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        isConnecting: false,
        syncError: e.toString(),
      );
    }
  }

  Future<void> retryConnection() async {
    try {
      state = state.copyWith(isConnecting: true, syncError: null);
      
      final isConnected = await _networkInfo.isConnected;
      state = state.copyWith(
        isOnline: isConnected,
        isConnecting: false,
      );

      if (isConnected) {
        await _handleReconnection();
      }
    } catch (e) {
      state = state.copyWith(
        isConnecting: false,
        syncError: 'Retry failed: $e',
      );
    }
  }

  void clearSyncError() {
    state = state.copyWith(syncError: null);
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncCheckTimer?.cancel();
    super.dispose();
  }
}
