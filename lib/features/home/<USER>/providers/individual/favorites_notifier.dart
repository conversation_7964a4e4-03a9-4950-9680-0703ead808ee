import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../domain/entities/favorite_entity.dart';
import '../../../data/repositories/home_repository.dart';

/// Individual Favorites State
class FavoritesState {
  final List<FavoriteEntity> favorites;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;
  final int pendingSyncCount;

  const FavoritesState({
    this.favorites = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
    this.pendingSyncCount = 0,
  });

  FavoritesState copyWith({
    List<FavoriteEntity>? favorites,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
    int? pendingSyncCount,
  }) {
    return FavoritesState(
      favorites: favorites ?? this.favorites,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      pendingSyncCount: pendingSyncCount ?? this.pendingSyncCount,
    );
  }

  bool get hasData => favorites.isNotEmpty;
  bool get hasError => error != null;
  bool get isStale => lastUpdated == null || 
      DateTime.now().difference(lastUpdated!).inMinutes > 15;
  bool get hasPendingSync => pendingSyncCount > 0;
}

/// Individual Favorites Notifier
class FavoritesNotifier extends StateNotifier<FavoritesState> {
  final HomeRepository _repository;
  Timer? _autoRefreshTimer;

  FavoritesNotifier(this._repository) : super(const FavoritesState()) {
    _setupAutoRefresh();
    loadFavorites();
  }

  void _setupAutoRefresh() {
    _autoRefreshTimer?.cancel();
    _autoRefreshTimer = Timer.periodic(const Duration(minutes: 15), (_) {
      if (mounted) {
        loadFavorites(forceRefresh: true);
      }
    });
  }

  Future<void> loadFavorites({bool forceRefresh = false}) async {
    if (!forceRefresh && state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
    );

    try {
      final result = await _repository.getFavorites();

      if (mounted) {
        result.fold(
          (error) => state = state.copyWith(
            isLoading: false,
            error: error.message,
          ),
          (favorites) => state = state.copyWith(
            favorites: favorites,
            isLoading: false,
            error: null,
            lastUpdated: DateTime.now(),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(
          isLoading: false,
          error: e.toString(),
        );
      }
    }
  }

  Future<void> reorderFavorites(List<String> orderedIds) async {
    // Optimistic update
    final reorderedFavorites = <FavoriteEntity>[];
    for (int i = 0; i < orderedIds.length; i++) {
      try {
        final favorite = state.favorites.firstWhere((f) => f.id == orderedIds[i]);
        reorderedFavorites.add(favorite.copyWith(sortOrder: i));
      } catch (e) {
        // Skip if favorite not found
        continue;
      }
    }

    state = state.copyWith(favorites: reorderedFavorites);

    // try {
    //   final result = await _repository.reorderFavorites(orderedIds);
    //
    //   if (mounted) {
    //     result.fold(
    //       (error) => state = state.copyWith(error: error.message),
    //       (_) => state = state.copyWith(
    //         error: null,
    //         pendingSyncCount: state.pendingSyncCount + 1,
    //       ),
    //     );
    //   }
    // } catch (e) {
    //   if (mounted) {
    //     state = state.copyWith(error: e.toString());
    //   }
    // }
  }

  Future<void> addToFavorites(FavoriteEntity favorite) async {
    // Optimistic update
    final updatedFavorites = [...state.favorites, favorite];
    state = state.copyWith(favorites: updatedFavorites);

    try {
      // Implementation would depend on repository method
      // final result = await _repository.addFavorite(favorite);
      // Handle result...
    } catch (e) {
      if (mounted) {
        // Revert on error
        final revertedFavorites = state.favorites.where((f) => f.id != favorite.id).toList();
        state = state.copyWith(
          favorites: revertedFavorites,
          error: e.toString(),
        );
      }
    }
  }

  Future<void> removeFromFavorites(String favoriteId) async {
    // Store original for potential revert
    final originalFavorites = state.favorites;
    
    // Optimistic update
    final updatedFavorites = state.favorites.where((f) => f.id != favoriteId).toList();
    state = state.copyWith(favorites: updatedFavorites);

    try {
      // Implementation would depend on repository method
      // final result = await _repository.removeFavorite(favoriteId);
      // Handle result...
    } catch (e) {
      if (mounted) {
        // Revert on error
        state = state.copyWith(
          favorites: originalFavorites,
          error: e.toString(),
        );
      }
    }
  }

  Future<void> refresh() async {
    await loadFavorites(forceRefresh: true);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  @override
  void dispose() {
    _autoRefreshTimer?.cancel();
    super.dispose();
  }
}
