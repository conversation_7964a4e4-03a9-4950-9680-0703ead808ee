import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../shared/utils/responsive_utils.dart';
import '../../../../../shared/widgets/responsive_widget.dart';
import '../../../../../shared/theme/app_colors.dart';
import '../../../domain/entities/favorite_entity.dart';
import '../../providers/home_provider.dart';

/// Individual Favorites Section Widget
/// This widget only rebuilds when favorites-related data changes
class IndividualFavoritesSection extends ConsumerWidget {
  const IndividualFavoritesSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch only favorites-specific providers
    final favorites = ref.watch(favoritesDataProvider);
    final isLoading = ref.watch(favoritesLoadingProvider);
    final error = ref.watch(favoritesErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);
    final pendingSyncCount = ref.watch(favoritesPendingSyncProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const ResponsiveText(
                  'Favorit Saya',
                  type: TextType.h4,
                  fontWeight: FontWeight.bold,
                ),
                Row(
                  children: [
                    if (isOffline) _buildOfflineChip(),
                    if (pendingSyncCount > 0) ...[
                      const SizedBox(width: 8),
                      _buildSyncChip(pendingSyncCount),
                    ],
                    const SizedBox(width: 8),
                    if (error != null) _buildErrorChip(),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        ref.read(individualFavoritesNotifierProvider.notifier).refresh();
                      },
                      child: Icon(
                        Icons.refresh,
                        color: isLoading ? Colors.grey : AppColors.primary,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Content
            if (isLoading && favorites.isEmpty)
              _buildFavoritesLoadingSkeleton()
            else if (error != null && favorites.isEmpty)
              _buildFavoritesError(context, ref, error)
            else if (favorites.isEmpty)
              _buildEmptyFavorites(context)
            else
              _buildFavoritesList(context, favorites),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesLoadingSkeleton() {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 4,
        itemBuilder: (context, index) {
          return Container(
            width: 80,
            margin: const EdgeInsets.only(right: 12),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 12,
                  width: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFavoritesError(BuildContext context, WidgetRef ref, String error) {
    return Container(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 32),
            const SizedBox(height: 8),
            ResponsiveText(
              'Error loading favorites',
              type: TextType.bodyMedium,
              color: Colors.red,
            ),
            const SizedBox(height: 4),
            ResponsiveText(
              error,
              type: TextType.bodySmall,
              color: Colors.grey,
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                ref.read(individualFavoritesNotifierProvider.notifier).refresh();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyFavorites(BuildContext context) {
    return Container(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite_border, color: Colors.grey, size: 32),
            const SizedBox(height: 8),
            ResponsiveText(
              'No favorites yet',
              type: TextType.bodyMedium,
              color: Colors.grey,
            ),
            const SizedBox(height: 4),
            ResponsiveText(
              'Add your frequently used services',
              type: TextType.bodySmall,
              color: Colors.grey,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesList(BuildContext context, List<FavoriteEntity> favorites) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final crossAxisCount = dimensions.isTablet ? 6 : 4;
        final aspectRatio = dimensions.isTablet ? 1.0 : 0.8;

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: aspectRatio,
            crossAxisSpacing: context.spacing.sm,
            mainAxisSpacing: context.spacing.sm,
          ),
          itemCount: favorites.length,
          itemBuilder: (context, index) {
            return _buildFavoriteItem(context, favorites[index], dimensions);
          },
        );
      },
    );
  }

  Widget _buildFavoriteItem(BuildContext context, FavoriteEntity favorite, ResponsiveDimensions dimensions) {
    return GestureDetector(
      onTap: () => _handleFavoriteTap(context, favorite),
      child: Container(
        padding: EdgeInsets.all(context.spacing.sm),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: dimensions.isTablet ? 40 : 32,
              height: dimensions.isTablet ? 40 : 32,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildFavoriteIcon(favorite, dimensions),
              ),
            ),
            const SizedBox(height: 4),
            ResponsiveText(
              favorite.name,
              type: dimensions.isTablet ? TextType.bodySmall : TextType.caption,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteIcon(FavoriteEntity favorite, ResponsiveDimensions dimensions) {
    final iconSize = dimensions.isTablet ? 20.0 : 16.0;

    if (favorite.iconUrl.isNotEmpty) {
      return Image.network(
        favorite.iconUrl,
        width: iconSize,
        height: iconSize,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackIcon(favorite.iconName, iconSize);
        },
      );
    } else {
      return _buildFallbackIcon(favorite.iconName, iconSize);
    }
  }

  Widget _buildFallbackIcon(String iconName, double size) {
    return Icon(
      _getIconData(iconName),
      size: size,
      color: AppColors.primary,
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'transfer': return Icons.send;
      case 'payment': return Icons.payment;
      case 'topup': return Icons.add_circle;
      case 'history': return Icons.history;
      case 'voucher': return Icons.local_offer;
      default: return Icons.apps;
    }
  }

  Widget _buildOfflineChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const ResponsiveText(
        'Offline',
        type: TextType.caption,
        color: Colors.white,
      ),
    );
  }

  Widget _buildSyncChip(int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ResponsiveText(
        '$count sync',
        type: TextType.caption,
        color: Colors.white,
      ),
    );
  }

  Widget _buildErrorChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.error_outline,
        color: Colors.white,
        size: 12,
      ),
    );
  }

  void _handleFavoriteTap(BuildContext context, FavoriteEntity favorite) {
    // Handle favorite tap
    print('Tapped on ${favorite.name}');
  }
}
