import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../shared/utils/responsive_utils.dart';
import '../../../../../shared/widgets/responsive_widget.dart';
import '../../../../../shared/theme/app_colors.dart';
import '../../providers/home_provider.dart';

/// Individual Balance Card Widget
/// This widget only rebuilds when balance-related data changes
class IndividualBalanceCard extends ConsumerWidget {
  const IndividualBalanceCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch only balance-specific providers
    final balance = ref.watch(balanceDataProvider);
    final isLoading = ref.watch(balanceLoadingProvider);
    final error = ref.watch(balanceErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);

    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(context.spacing.lg),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with status indicators
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const ResponsiveText(
                      'Saldo Anda',
                      type: TextType.bodyMedium,
                      color: Colors.white70,
                    ),
                    Row(
                      children: [
                        if (isOffline) _buildOfflineIndicator(),
                        const SizedBox(width: 8),
                        if (error != null) _buildErrorIndicator(),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: () {
                            ref.read(individualBalanceNotifierProvider.notifier).refresh();
                          },
                          child: Icon(
                            Icons.refresh,
                            color: isLoading ? Colors.white54 : Colors.white,
                            size: dimensions.isTablet ? 24 : 20,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const ResponsiveSpacing(size: SpacingSize.sm),

                // Balance display with loading state
                if (isLoading && balance == null)
                  _buildBalanceLoadingSkeleton(dimensions)
                else if (error != null && balance == null)
                  _buildBalanceError(context, error)
                else
                  _buildBalanceContent(context, ref, balance, dimensions),

                const ResponsiveSpacing(size: SpacingSize.md),

                // Quick actions
                _buildQuickActions(context, dimensions),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBalanceContent(BuildContext context, WidgetRef ref, balance, ResponsiveDimensions dimensions) {
    return Row(
      children: [
        Expanded(
          child: ResponsiveText(
            balance?.isHidden == true
                ? '${balance?.currency ?? 'Rp'} ••••••••'
                : '${balance?.currency ?? 'Rp'} ${_formatAmount(balance?.amount ?? 0)}',
            type: dimensions.isTablet ? TextType.h2 : TextType.h3,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        GestureDetector(
          onTap: () {
            ref.read(individualBalanceNotifierProvider.notifier).toggleVisibility();
          },
          child: Icon(
            balance?.isHidden == true ? Icons.visibility : Icons.visibility_off,
            color: Colors.white,
            size: dimensions.isTablet ? 24 : 20,
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceLoadingSkeleton(ResponsiveDimensions dimensions) {
    return Container(
      height: dimensions.isTablet ? 32 : 28,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  Widget _buildBalanceError(BuildContext context, String error) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ResponsiveText(
          'Error loading balance',
          type: TextType.bodyMedium,
          color: Colors.white70,
        ),
        ResponsiveText(
          error,
          type: TextType.bodySmall,
          color: Colors.white60,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context, ResponsiveDimensions dimensions) {
    return Row(
      children: [
        Expanded(child: _buildActionButton(context, Icons.send, 'Transfer', dimensions)),
        const SizedBox(width: 8),
        Expanded(child: _buildActionButton(context, Icons.payment, 'Pay', dimensions)),
        const SizedBox(width: 8),
        Expanded(child: _buildActionButton(context, Icons.add_circle, 'Top Up', dimensions)),
        if (dimensions.isTablet) ...[
          const SizedBox(width: 8),
          Expanded(child: _buildActionButton(context, Icons.more_horiz, 'More', dimensions)),
        ],
      ],
    );
  }

  Widget _buildActionButton(BuildContext context, IconData icon, String label, ResponsiveDimensions dimensions) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: dimensions.isTablet ? 12 : 8,
          horizontal: 8,
        ),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: dimensions.isTablet ? 24 : 20,
            ),
            const SizedBox(height: 4),
            ResponsiveText(
              label,
              type: dimensions.isTablet ? TextType.bodySmall : TextType.caption,
              color: Colors.white,
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const ResponsiveText(
        'Offline',
        type: TextType.caption,
        color: Colors.white,
      ),
    );
  }

  Widget _buildErrorIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.error_outline,
        color: Colors.white,
        size: 12,
      ),
    );
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }
}
