import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../shared/utils/responsive_utils.dart';
import '../../../../../shared/widgets/responsive_widget.dart';
import '../../../../../shared/theme/app_colors.dart';
import '../../../domain/entities/banner_entity.dart';
import '../../providers/home_provider.dart';

/// Individual Banners Section Widget
/// This widget only rebuilds when banners-related data changes
class IndividualBannersSection extends ConsumerWidget {
  const IndividualBannersSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch only banners-specific providers
    final banners = ref.watch(activeBannersProvider);
    final isLoading = ref.watch(bannersLoadingProvider);
    final error = ref.watch(bannersErrorStateProvider);
    final isOffline = ref.watch(isOfflineStatusProvider);
    final currentIndex = ref.watch(currentBannerIndexProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const ResponsiveText(
                  'Promo Spesial untuk Kamu!',
                  type: TextType.h4,
                  fontWeight: FontWeight.bold,
                ),
                Row(
                  children: [
                    if (isOffline) _buildOfflineChip(),
                    const SizedBox(width: 8),
                    if (error != null) _buildErrorChip(),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        ref.read(individualBannersNotifierProvider.notifier).refresh();
                      },
                      child: Icon(
                        Icons.refresh,
                        color: isLoading ? Colors.grey : AppColors.primary,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Content
            if (isLoading && banners.isEmpty)
              _buildBannersLoadingSkeleton()
            else if (error != null && banners.isEmpty)
              _buildBannersError(context, ref, error)
            else if (banners.isEmpty)
              _buildEmptyBanners(context)
            else
              _buildBannersCarousel(context, ref, banners, currentIndex),
          ],
        ),
      ),
    );
  }

  Widget _buildBannersLoadingSkeleton() {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final height = dimensions.isTablet ? 160.0 : 120.0;

        return Container(
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(16),
          ),
        );
      },
    );
  }

  Widget _buildBannersError(BuildContext context, WidgetRef ref, String error) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final height = dimensions.isTablet ? 160.0 : 120.0;

        return Container(
          height: height,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, color: Colors.red, size: 32),
                const SizedBox(height: 8),
                ResponsiveText(
                  'Error loading promos',
                  type: TextType.bodyMedium,
                  color: Colors.red,
                ),
                const SizedBox(height: 4),
                ResponsiveText(
                  error,
                  type: TextType.bodySmall,
                  color: Colors.grey,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () {
                    ref.read(individualBannersNotifierProvider.notifier).refresh();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyBanners(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final height = dimensions.isTablet ? 160.0 : 120.0;

        return Container(
          height: height,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.campaign, color: Colors.grey, size: 32),
                const SizedBox(height: 8),
                ResponsiveText(
                  'No promos available',
                  type: TextType.bodyMedium,
                  color: Colors.grey,
                ),
                const SizedBox(height: 4),
                ResponsiveText(
                  'Check back later for exciting offers',
                  type: TextType.bodySmall,
                  color: Colors.grey,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBannersCarousel(BuildContext context, WidgetRef ref, List<BannerEntity> banners, int currentIndex) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final height = dimensions.isTablet ? 160.0 : 120.0;

        return Column(
          children: [
            // Carousel
            SizedBox(
              height: height,
              child: PageView.builder(
                controller: PageController(initialPage: currentIndex),
                onPageChanged: (index) {
                  ref.read(individualBannersNotifierProvider.notifier).goToBanner(index);
                },
                itemCount: banners.length,
                itemBuilder: (context, index) {
                  return _buildBannerItem(context, ref, banners[index], dimensions);
                },
              ),
            ),

            // Indicators
            if (banners.length > 1) ...[
              const SizedBox(height: 12),
              _buildIndicators(context, ref, banners.length, currentIndex),
            ],
          ],
        );
      },
    );
  }

  Widget _buildBannerItem(BuildContext context, WidgetRef ref, BannerEntity banner, ResponsiveDimensions dimensions) {
    return GestureDetector(
      onTap: () {
        ref.read(individualBannersNotifierProvider.notifier).onBannerTapped(banner);
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Banner image or placeholder
              Container(
                width: double.infinity,
                height: double.infinity,
                color: AppColors.primary.withOpacity(0.1),
                child: banner.imageUrl.isNotEmpty
                    ? Image.network(
                        banner.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildBannerPlaceholder(context, banner);
                        },
                      )
                    : _buildBannerPlaceholder(context, banner),
              ),

              // Banner content overlay
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(context.spacing.md),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                  child: ResponsiveText(
                    banner.title,
                    type: dimensions.isTablet ? TextType.bodyMedium : TextType.bodySmall,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBannerPlaceholder(BuildContext context, BannerEntity banner) {
    return Container(
      color: AppColors.primary.withOpacity(0.1),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image,
              size: 40,
              color: AppColors.primary.withOpacity(0.5),
            ),
            const ResponsiveSpacing(size: SpacingSize.sm),
            ResponsiveText(
              banner.title,
              type: TextType.bodySmall,
              color: AppColors.primary,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIndicators(BuildContext context, WidgetRef ref, int count, int currentIndex) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(count, (index) {
        return GestureDetector(
          onTap: () {
            ref.read(individualBannersNotifierProvider.notifier).goToBanner(index);
          },
          child: Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index == currentIndex
                  ? AppColors.primary
                  : AppColors.primary.withOpacity(0.3),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildOfflineChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const ResponsiveText(
        'Offline',
        type: TextType.caption,
        color: Colors.white,
      ),
    );
  }

  Widget _buildErrorChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.error_outline,
        color: Colors.white,
        size: 12,
      ),
    );
  }
}
