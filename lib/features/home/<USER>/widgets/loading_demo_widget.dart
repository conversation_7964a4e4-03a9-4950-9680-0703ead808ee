import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/widgets/responsive_widget.dart';
import '../providers/home_provider.dart';

/// Widget to demonstrate independent loading behavior
class LoadingDemoWidget extends ConsumerWidget {
  const LoadingDemoWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.speed, color: Colors.blue),
                const SizedBox(width: 8),
                const ResponsiveText(
                  'Independent Loading Demo',
                  type: TextType.h5,
                  fontWeight: FontWeight.bold,
                ),
                const Spacer(),
                ElevatedButton(
                  onPressed: () => _refreshAll(ref),
                  child: const Text('Refresh All'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Balance Section Status
            _buildSectionStatus(
              'Balance',
              ref.watch(balanceLoadingProvider),
              ref.watch(balanceHasDataProvider),
              ref.watch(balanceErrorStateProvider),
              () => ref.read(individualBalanceNotifierProvider.notifier).refresh(),
            ),
            
            const SizedBox(height: 8),
            
            // Favorites Section Status
            _buildSectionStatus(
              'Favorites',
              ref.watch(favoritesLoadingProvider),
              ref.watch(favoritesHasDataProvider),
              ref.watch(favoritesErrorStateProvider),
              () => ref.read(individualFavoritesNotifierProvider.notifier).refresh(),
            ),
            
            const SizedBox(height: 8),
            
            // Banners Section Status
            _buildSectionStatus(
              'Banners',
              ref.watch(bannersLoadingProvider),
              ref.watch(activeBannersProvider).isNotEmpty,
              ref.watch(bannersErrorStateProvider),
              () => ref.read(individualBannersNotifierProvider.notifier).refresh(),
            ),
            
            const SizedBox(height: 16),
            
            // Overall Status
            _buildOverallStatus(ref),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionStatus(
    String sectionName,
    bool isLoading,
    bool hasData,
    String? error,
    VoidCallback onRefresh,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          // Section name
          SizedBox(
            width: 80,
            child: ResponsiveText(
              sectionName,
              type: TextType.bodyMedium,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Status indicator
          _buildStatusIndicator(isLoading, hasData, error),
          
          const SizedBox(width: 16),
          
          // Status text
          Expanded(
            child: ResponsiveText(
              _getStatusText(isLoading, hasData, error),
              type: TextType.bodySmall,
              color: _getStatusColor(isLoading, hasData, error),
            ),
          ),
          
          // Refresh button
          IconButton(
            onPressed: onRefresh,
            icon: Icon(
              Icons.refresh,
              size: 16,
              color: isLoading ? Colors.grey : Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(bool isLoading, bool hasData, String? error) {
    if (isLoading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    } else if (error != null) {
      return const Icon(Icons.error, color: Colors.red, size: 16);
    } else if (hasData) {
      return const Icon(Icons.check_circle, color: Colors.green, size: 16);
    } else {
      return const Icon(Icons.radio_button_unchecked, color: Colors.grey, size: 16);
    }
  }

  String _getStatusText(bool isLoading, bool hasData, String? error) {
    if (isLoading) {
      return 'Loading...';
    } else if (error != null) {
      return 'Error: ${error.length > 20 ? '${error.substring(0, 20)}...' : error}';
    } else if (hasData) {
      return 'Loaded successfully';
    } else {
      return 'No data';
    }
  }

  Color _getStatusColor(bool isLoading, bool hasData, String? error) {
    if (isLoading) {
      return Colors.blue;
    } else if (error != null) {
      return Colors.red;
    } else if (hasData) {
      return Colors.green;
    } else {
      return Colors.grey;
    }
  }

  Widget _buildOverallStatus(WidgetRef ref) {
    final hasAnyData = ref.watch(homeHasAnyDataProvider);
    final isAnyLoading = ref.watch(homeIsAnyLoadingProvider);
    final hasAnyError = ref.watch(homeHasAnyErrorProvider);
    final totalPendingSync = ref.watch(homeTotalPendingSyncProvider);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ResponsiveText(
            'Overall Status',
            type: TextType.bodyMedium,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildStatusChip('Data', hasAnyData ? 'Available' : 'None', hasAnyData),
              const SizedBox(width: 8),
              _buildStatusChip('Loading', isAnyLoading ? 'Yes' : 'No', !isAnyLoading),
              const SizedBox(width: 8),
              _buildStatusChip('Errors', hasAnyError ? 'Yes' : 'No', !hasAnyError),
            ],
          ),
          if (totalPendingSync > 0) ...[
            const SizedBox(height: 8),
            _buildStatusChip('Pending Sync', '$totalPendingSync items', false),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, String value, bool isGood) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isGood ? Colors.green[100] : Colors.orange[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: ResponsiveText(
        '$label: $value',
        type: TextType.caption,
        color: isGood ? Colors.green[800] : Colors.orange[800],
      ),
    );
  }

  void _refreshAll(WidgetRef ref) {
    // Refresh all sections independently - they don't wait for each other
    ref.read(individualBalanceNotifierProvider.notifier).refresh();
    ref.read(individualFavoritesNotifierProvider.notifier).refresh();
    ref.read(individualBannersNotifierProvider.notifier).refresh();
  }
}
