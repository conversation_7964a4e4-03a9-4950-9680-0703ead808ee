// lib/shared/widgets/responsive_widgets.dart
import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';
import '../theme/app_colors.dart';

/// Responsive text widget with automatic sizing
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextType type;
  final Color? color;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
  this.text, {
  super.key,
  this.type = TextType.bodyMedium,
  this.color,
  this.fontWeight,
  this.textAlign,
  this.maxLines,
  this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final typography = ResponsiveUtils.getTypography(context);

    double fontSize;
    switch (type) {
      case TextType.h1:
        fontSize = typography.h1;
        break;
      case TextType.h2:
        fontSize = typography.h2;
        break;
      case TextType.h3:
        fontSize = typography.h3;
        break;
      case TextType.h4:
        fontSize = typography.h4;
        break;
      case TextType.bodyLarge:
        fontSize = typography.bodyLarge;
        break;
      case TextType.bodyMedium:
        fontSize = typography.bodyMedium;
        break;
      case TextType.bodySmall:
        fontSize = typography.bodySmall;
        break;
      case TextType.caption:
        fontSize = typography.caption;
        break;
    }

    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        color: color ?? AppColors.textPrimary,
        fontWeight: fontWeight ?? _getDefaultFontWeight(),
        height: 1.4,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  FontWeight _getDefaultFontWeight() {
    switch (type) {
      case TextType.h1:
      case TextType.h2:
        return FontWeight.bold;
      case TextType.h3:
      case TextType.h4:
        return FontWeight.w600;
      default:
        return FontWeight.normal;
    }
  }
}

enum TextType {
  h1,
  h2,
  h3,
  h4,
  bodyLarge,
  bodyMedium,
  bodySmall,
  caption,
}

/// Responsive button widget
class ResponsiveButton extends StatelessWidget {
final String text;
final VoidCallback? onPressed;
final ButtonType type;
final ButtonSize size;
final bool isLoading;
final Widget? icon;
final Color? backgroundColor;
final Color? textColor;

const ResponsiveButton({
super.key,
required this.text,
this.onPressed,
this.type = ButtonType.primary,
this.size = ButtonSize.large,
this.isLoading = false,
this.icon,
this.backgroundColor,
this.textColor,
});

@override
Widget build(BuildContext context) {
  final buttonDimensions = ResponsiveUtils.getButtonDimensions(context);

  double height;
  switch (size) {
    case ButtonSize.small:
      height = buttonDimensions.heightSmall;
      break;
    case ButtonSize.medium:
      height = buttonDimensions.heightMedium;
      break;
    case ButtonSize.large:
      height = buttonDimensions.heightLarge;
      break;
  }

  // Use ConstrainedBox instead of SizedBox to avoid infinite width constraints
  return ConstrainedBox(
    constraints: BoxConstraints(
      minHeight: height,
      minWidth: 0,
    ),
    child: _buildButton(context, buttonDimensions, height),
  );
}

Widget _buildButton(BuildContext context, ResponsiveButtonDimensions buttonDimensions, double height) {
  final isEnabled = onPressed != null && !isLoading;

  switch (type) {
    case ButtonType.primary:
      return ElevatedButton(
        onPressed: isEnabled ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColors.primary,
          disabledBackgroundColor: AppColors.primary.withOpacity(0.6),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonDimensions.borderRadius),
          ),
          elevation: isLoading ? 0 : 2,
          minimumSize: Size(0, height), // Don't force width, only height
        ),
        child: _buildButtonContent(buttonDimensions.fontSize, textColor ?? Colors.white),
      );

    case ButtonType.secondary:
      return OutlinedButton(
        onPressed: isEnabled ? onPressed : null,
        style: OutlinedButton.styleFrom(
          foregroundColor: textColor ?? AppColors.primary,
          side: BorderSide(
            color: isEnabled
                ? (backgroundColor ?? AppColors.primary)
                : Colors.grey.shade300,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonDimensions.borderRadius),
          ),
          minimumSize: Size(0, height), // Don't force width, only height
        ),
        child: _buildButtonContent(
          buttonDimensions.fontSize,
          isEnabled
              ? (textColor ?? AppColors.primary)
              : Colors.grey.shade400,
        ),
      );

    case ButtonType.text:
      return TextButton(
        onPressed: isEnabled ? onPressed : null,
        style: TextButton.styleFrom(
          foregroundColor: textColor ?? AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonDimensions.borderRadius),
          ),
          minimumSize: Size(0, height), // Don't force width, only height
        ),
        child: _buildButtonContent(
          buttonDimensions.fontSize,
          isEnabled
              ? (textColor ?? AppColors.primary)
              : Colors.grey.shade400,
        ),
      );
  }
}

Widget _buildButtonContent(double fontSize, Color color) {
  if (isLoading) {
    return SizedBox(
      height: 20,
      width: 20,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(color),
      ),
    );
  }

  if (icon != null) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        icon!,
        const SizedBox(width: 8),
        Text(
          text,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  return Text(
    text,
    style: TextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w600,
      color: color,
    ),
  );
}
}

enum ButtonType { primary, secondary, text }
enum ButtonSize { small, medium, large }

/// Responsive spacing widget
class ResponsiveSpacing extends StatelessWidget {
  final SpacingSize size;
  final Axis direction;

  const ResponsiveSpacing({
  super.key,
  required this.size,
  this.direction = Axis.vertical,
  });

  @override
  Widget build(BuildContext context) {
    final spacing = ResponsiveUtils.getSpacing(context);

    double space;
    switch (size) {
      case SpacingSize.xs:
        space = spacing.xs;
        break;
      case SpacingSize.sm:
        space = spacing.sm;
        break;
      case SpacingSize.md:
        space = spacing.md;
        break;
      case SpacingSize.lg:
        space = spacing.lg;
        break;
      case SpacingSize.xl:
        space = spacing.xl;
        break;
    }

    return direction == Axis.vertical
        ? SizedBox(height: space)
        : SizedBox(width: space);
  }
}

enum SpacingSize { xs, sm, md, lg, xl }

/// Responsive container with automatic padding
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final bool useHorizontalPadding;
  final bool useVerticalPadding;
  final EdgeInsets? customPadding;
  final Color? backgroundColor;
  final BoxDecoration? decoration;

  const ResponsiveContainer({
  super.key,
  required this.child,
  this.useHorizontalPadding = true,
  this.useVerticalPadding = false,
  this.customPadding,
  this.backgroundColor,
  this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = ResponsiveUtils.getDimensions(context);

    EdgeInsets padding = EdgeInsets.zero;

    if (customPadding != null) {
      padding = customPadding!;
    } else {
      padding = EdgeInsets.only(
        left: useHorizontalPadding ? dimensions.horizontalPadding : 0,
        right: useHorizontalPadding ? dimensions.horizontalPadding : 0,
        top: useVerticalPadding ? dimensions.verticalPadding : 0,
        bottom: useVerticalPadding ? dimensions.verticalPadding : 0,
      );
    }

    return Container(
      padding: padding,
      decoration: decoration ?? (backgroundColor != null
          ? BoxDecoration(color: backgroundColor)
          : null),
      child: child,
    );
  }
}

/// Responsive input field
class ResponsiveTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final String? hintText;
  final String? labelText;
  final bool isRequired;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final Function(String)? onSubmitted;
  final Function(String)? onChanged;
  final bool obscureText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? errorText;
  final bool enabled;

  const ResponsiveTextField({
  super.key,
  this.controller,
  this.focusNode,
  this.hintText,
  this.labelText,
  this.isRequired = false,
  this.keyboardType,
  this.textInputAction,
  this.onSubmitted,
  this.onChanged,
  this.obscureText = false,
  this.prefixIcon,
  this.suffixIcon,
  this.errorText,
  this.enabled = true,
  });

  @override
  State<ResponsiveTextField> createState() => _ResponsiveTextFieldState();
}

class _ResponsiveTextFieldState extends State<ResponsiveTextField> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final dimensions = context.responsive;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.labelText != null) ...[
          ResponsiveText(
            widget.labelText! + (widget.isRequired ? ' *' : ''),
            type: TextType.bodyMedium,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
          ResponsiveSpacing(size: SpacingSize.xs),
        ],

        Container(
          decoration: BoxDecoration(
            color: widget.enabled ? Colors.grey.shade50 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: widget.errorText != null
                  ? AppColors.error
                  : _isFocused
                  ? AppColors.primary
                  : Colors.grey.shade300,
              width: _isFocused ? 2 : 1,
            ),
          ),
          child: TextField(
            controller: widget.controller,
            focusNode: _focusNode,
            keyboardType: widget.keyboardType,
            textInputAction: widget.textInputAction,
            onSubmitted: widget.onSubmitted,
            onChanged: widget.onChanged,
            obscureText: widget.obscureText,
            enabled: widget.enabled,
            style: TextStyle(
              fontSize: typography.bodyMedium,
              color: widget.enabled ? AppColors.textPrimary : Colors.grey.shade500,
            ),
            decoration: InputDecoration(
              hintText: widget.hintText,
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: dimensions.horizontalPadding * 0.67, // 16px
                vertical: dimensions.isSmallScreen ? 12.0 : 16.0,
              ),
              hintStyle: TextStyle(
                color: Colors.grey.shade500,
                fontSize: typography.bodyMedium,
              ),
              prefixIcon: widget.prefixIcon,
              suffixIcon: widget.suffixIcon,
            ),
          ),
        ),

        if (widget.errorText != null) ...[
          ResponsiveSpacing(size: SpacingSize.xs),
          ResponsiveText(
            widget.errorText!,
            type: TextType.bodySmall,
            color: AppColors.error,
          ),
        ],
      ],
    );
  }
}

/// Responsive image widget
class ResponsiveImage extends StatelessWidget {
  final String? imagePath;
  final IconData? icon;
  final Color? iconColor;
  final ImageSize size;
  final Color? backgroundColor;
  final BoxFit fit;
  final Widget? placeholder;

  const ResponsiveImage({
  super.key,
  this.imagePath,
  this.icon,
  this.iconColor,
  this.size = ImageSize.large,
  this.backgroundColor,
  this.fit = BoxFit.cover,
  this.placeholder,
  });

  @override
  Widget build(BuildContext context) {
    final image = ResponsiveUtils.getImage(context);

    double imageSize;
    switch (size) {
      case ImageSize.small:
        imageSize = image.small;
        break;
      case ImageSize.medium:
        imageSize = image.medium;
        break;
      case ImageSize.large:
        imageSize = image.large;
        break;
    }

    return Container(
      height: imageSize,
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey.shade100,
        borderRadius: BorderRadius.circular(20),
      ),
      child: _buildContent(imageSize),
    );
  }

  Widget _buildContent(double imageSize) {
    if (imagePath != null) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Image.asset(
          imagePath!,
          fit: fit,
          errorBuilder: (context, error, stackTrace) {
            return _buildPlaceholder(imageSize);
          },
        ),
      );
    }

    return _buildPlaceholder(imageSize);
  }

  Widget _buildPlaceholder(double imageSize) {
    if (placeholder != null) return placeholder!;

    return Center(
      child: Icon(
        icon ?? Icons.image_outlined,
        size: imageSize * 0.3,
        color: iconColor ?? Colors.grey.shade400,
      ),
    );
  }
}

enum ImageSize { small, medium, large }

/// Responsive error card widget
class ResponsiveErrorCard extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;

  const ResponsiveErrorCard({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.icon,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(context.spacing.lg),
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.red[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon ?? Icons.error_outline,
                color: textColor ?? Colors.red,
                size: dimensions.isTablet ? 40 : 32,
              ),
              const ResponsiveSpacing(size: SpacingSize.sm),
              ResponsiveText(
                title,
                type: TextType.bodyMedium,
                fontWeight: FontWeight.w500,
                color: textColor ?? Colors.red,
                textAlign: TextAlign.center,
              ),
              const ResponsiveSpacing(size: SpacingSize.xs),
              ResponsiveText(
                message,
                type: TextType.bodySmall,
                color: Colors.grey,
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              if (onRetry != null) ...[
                const ResponsiveSpacing(size: SpacingSize.sm),
                ResponsiveButton(
                  text: 'Retry',
                  onPressed: onRetry,
                  size: ButtonSize.small,
                  backgroundColor: AppColors.primary,
                  textColor: Colors.white,
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}

/// Responsive grid widget
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;
  final double? childAspectRatio;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
    this.childAspectRatio,
    this.shrinkWrap = true,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        int columns;
        switch (dimensions.screenCategory) {
          case ScreenCategory.mobile:
            columns = mobileColumns ?? 2;
            break;
          case ScreenCategory.tablet:
            columns = tabletColumns ?? 4;
            break;
          case ScreenCategory.desktop:
            columns = desktopColumns ?? 6;
            break;
        }

        return GridView.builder(
          shrinkWrap: shrinkWrap,
          physics: physics ?? const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            childAspectRatio: childAspectRatio ?? 1.0,
            crossAxisSpacing: crossAxisSpacing ?? context.spacing.sm,
            mainAxisSpacing: mainAxisSpacing ?? context.spacing.sm,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}

/// Responsive card widget
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        final cardPadding = padding ?? EdgeInsets.all(
          dimensions.isTablet ? context.spacing.lg : context.spacing.md,
        );

        final cardElevation = elevation ?? (dimensions.isTablet ? 6.0 : 4.0);

        final cardBorderRadius = borderRadius ?? BorderRadius.circular(
          dimensions.isTablet ? 16.0 : 12.0,
        );

        return Card(
          elevation: cardElevation,
          color: backgroundColor,
          shape: RoundedRectangleBorder(borderRadius: cardBorderRadius),
          child: InkWell(
            onTap: onTap,
            borderRadius: cardBorderRadius,
            child: Padding(
              padding: cardPadding,
              child: child,
            ),
          ),
        );
      },
    );
  }
}

/// Responsive icon widget
class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final Color? color;
  final IconSize size;

  const ResponsiveIcon({
    super.key,
    required this.icon,
    this.color,
    this.size = IconSize.medium,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, dimensions) {
        double iconSize;
        switch (size) {
          case IconSize.small:
            iconSize = dimensions.isTablet ? 20.0 : 16.0;
            break;
          case IconSize.medium:
            iconSize = dimensions.isTablet ? 28.0 : 24.0;
            break;
          case IconSize.large:
            iconSize = dimensions.isTablet ? 36.0 : 32.0;
            break;
          case IconSize.extraLarge:
            iconSize = dimensions.isTablet ? 48.0 : 40.0;
            break;
        }

        return Icon(
          icon,
          size: iconSize,
          color: color,
        );
      },
    );
  }
}

enum IconSize { small, medium, large, extraLarge }