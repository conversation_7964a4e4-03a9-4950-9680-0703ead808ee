// Enhanced Responsive Utils with improved performance and features
import 'package:flutter/material.dart';

/// Enhanced responsive utility class for consistent sizing across the app
class ResponsiveUtils {
  static ResponsiveUtils? _instance;
  static ResponsiveUtils get instance => _instance ??= ResponsiveUtils._();
  ResponsiveUtils._();

  // Cache for performance optimization
  static final Map<String, dynamic> _cache = {};

  /// Enhanced screen size breakpoints with more granular control
  static const double mobileXS = 320;   // Very small phones
  static const double mobileSmall = 360; // Small phones
  static const double mobileMedium = 375; // Medium phones (iPhone SE)
  static const double mobileLarge = 414;  // Large phones (iPhone Pro)
  static const double mobileXL = 480;     // Extra large phones
  static const double tabletSmall = 600;  // Small tablets
  static const double tablet = 768;       // Standard tablets
  static const double tabletLarge = 1024; // Large tablets
  static const double desktop = 1200;     // Desktop
  static const double desktopLarge = 1440; // Large desktop

  /// Enhanced height breakpoints
  static const double heightXS = 480;     // Very short screens
  static const double heightSmall = 568;  // iPhone SE height
  static const double heightMedium = 667; // iPhone 8 height
  static const double heightLarge = 812;  // iPhone X height
  static const double heightXL = 926;     // iPhone Pro Max height

  /// Density breakpoints for high-DPI displays
  static const double densityLow = 1.0;
  static const double densityMedium = 2.0;
  static const double densityHigh = 3.0;

  /// Get enhanced responsive dimensions with caching for performance
  static ResponsiveDimensions getDimensions(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final size = mediaQuery.size;
    final padding = mediaQuery.viewInsets;
    final devicePixelRatio = mediaQuery.devicePixelRatio;

    // Create cache key
    final cacheKey = '${size.width}_${size.height}_$devicePixelRatio';

    // Return cached result if available
    if (_cache.containsKey(cacheKey)) {
      return _cache[cacheKey] as ResponsiveDimensions;
    }

    final dimensions = ResponsiveDimensions(
      screenSize: size,
      padding: padding,
      devicePixelRatio: devicePixelRatio,
      isXSScreen: size.height < heightXS,
      isSmallScreen: size.height < heightMedium,
      isVerySmallScreen: size.height < heightSmall,
      isLargeScreen: size.height > heightLarge,
      isXLScreen: size.height > heightXL,
      isLandscape: size.width > size.height,
      isTablet: size.shortestSide >= tablet,
      isMobile: size.shortestSide < tablet,
      isDesktop: size.shortestSide >= desktop,
      deviceType: _getDeviceType(size),
      screenCategory: _getScreenCategory(size),
      densityCategory: _getDensityCategory(devicePixelRatio),
    );

    // Cache the result
    _cache[cacheKey] = dimensions;

    return dimensions;
  }

  static DeviceType _getDeviceType(Size size) {
    final shortestSide = size.shortestSide;

    if (shortestSide >= desktopLarge) return DeviceType.desktopLarge;
    if (shortestSide >= desktop) return DeviceType.desktop;
    if (shortestSide >= tabletLarge) return DeviceType.tabletLarge;
    if (shortestSide >= tablet) return DeviceType.tablet;
    if (shortestSide >= tabletSmall) return DeviceType.tabletSmall;
    if (shortestSide >= mobileXL) return DeviceType.mobileXL;
    if (shortestSide >= mobileLarge) return DeviceType.mobileLarge;
    if (shortestSide >= mobileMedium) return DeviceType.mobileMedium;
    if (shortestSide >= mobileSmall) return DeviceType.mobileSmall;
    return DeviceType.mobileXS;
  }

  static ScreenCategory _getScreenCategory(Size size) {
    if (size.shortestSide >= desktop) return ScreenCategory.desktop;
    if (size.shortestSide >= tablet) return ScreenCategory.tablet;
    return ScreenCategory.mobile;
  }

  static DensityCategory _getDensityCategory(double devicePixelRatio) {
    if (devicePixelRatio >= densityHigh) return DensityCategory.high;
    if (devicePixelRatio >= densityMedium) return DensityCategory.medium;
    return DensityCategory.low;
  }

  /// Clear cache when needed (e.g., orientation change)
  static void clearCache() {
    _cache.clear();
  }

  /// Get responsive spacing values
  static ResponsiveSpacingData getSpacing(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveSpacingData(  // Renamed
      dimensions: dimensions,
      xs: dimensions.isVerySmallScreen ? 4.0 : 8.0,
      sm: dimensions.isVerySmallScreen ? 8.0 : dimensions.isSmallScreen ? 12.0 : 16.0,
      md: dimensions.isVerySmallScreen ? 12.0 : dimensions.isSmallScreen ? 16.0 : 24.0,
      lg: dimensions.isVerySmallScreen ? 16.0 : dimensions.isSmallScreen ? 24.0 : 32.0,
      xl: dimensions.isVerySmallScreen ? 24.0 : dimensions.isSmallScreen ? 32.0 : 48.0,
    );
  }

  /// Get responsive typography
  static ResponsiveTypographyData getTypography(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveTypographyData(  // Renamed
      dimensions: dimensions,
      h1: dimensions.isVerySmallScreen ? 24.0 : dimensions.isSmallScreen ? 28.0 : 32.0,
      h2: dimensions.isVerySmallScreen ? 20.0 : dimensions.isSmallScreen ? 24.0 : 28.0,
      h3: dimensions.isVerySmallScreen ? 18.0 : dimensions.isSmallScreen ? 20.0 : 24.0,
      h4: dimensions.isVerySmallScreen ? 16.0 : dimensions.isSmallScreen ? 18.0 : 20.0,
      bodyLarge: dimensions.isSmallScreen ? 16.0 : 18.0,
      bodyMedium: dimensions.isSmallScreen ? 14.0 : 16.0,
      bodySmall: dimensions.isSmallScreen ? 12.0 : 14.0,
      caption: dimensions.isSmallScreen ? 10.0 : 12.0,
    );
  }

  /// Get responsive button dimensions
  static ResponsiveButtonData getButton(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveButtonData(  // Renamed
      dimensions: dimensions,
      heightLarge: dimensions.isSmallScreen ? 48.0 : 56.0,
      heightMedium: dimensions.isSmallScreen ? 40.0 : 48.0,
      heightSmall: dimensions.isSmallScreen ? 32.0 : 40.0,
      borderRadius: 12.0,
      fontSize: dimensions.isSmallScreen ? 14.0 : 16.0,
    );
  }

  /// Get responsive button dimensions (alternative method)
  static ResponsiveButtonDimensions getButtonDimensions(BuildContext context) {  // Renamed method
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 576) {
      return const ResponsiveButtonDimensions(
        heightSmall: 36,
        heightMedium: 44,
        heightLarge: 52,
        fontSize: 14,
        borderRadius: 8,
      );
    } else if (screenWidth < 768) {
      return const ResponsiveButtonDimensions(
        heightSmall: 40,
        heightMedium: 48,
        heightLarge: 56,
        fontSize: 15,
        borderRadius: 10,
      );
    } else {
      return const ResponsiveButtonDimensions(
        heightSmall: 44,
        heightMedium: 52,
        heightLarge: 60,
        fontSize: 16,
        borderRadius: 12,
      );
    }
  }

  /// Get responsive image dimensions
  static ResponsiveImageData getImage(BuildContext context) {  // Renamed
    final dimensions = getDimensions(context);

    return ResponsiveImageData(  // Renamed
      dimensions: dimensions,
      large: dimensions.isLandscape
          ? dimensions.screenSize.height * 0.4
          : dimensions.isVerySmallScreen
          ? 180.0
          : dimensions.isSmallScreen
          ? 220.0
          : 280.0,
      medium: dimensions.isVerySmallScreen ? 120.0 : dimensions.isSmallScreen ? 150.0 : 200.0,
      small: dimensions.isVerySmallScreen ? 80.0 : dimensions.isSmallScreen ? 100.0 : 120.0,
    );
  }
}

/// Enhanced responsive dimensions data class
class ResponsiveDimensions {
  final Size screenSize;
  final EdgeInsets padding;
  final double devicePixelRatio;
  final bool isXSScreen;
  final bool isSmallScreen;
  final bool isVerySmallScreen;
  final bool isLargeScreen;
  final bool isXLScreen;
  final bool isLandscape;
  final bool isTablet;
  final bool isMobile;
  final bool isDesktop;
  final DeviceType deviceType;
  final ScreenCategory screenCategory;
  final DensityCategory densityCategory;

  const ResponsiveDimensions({
    required this.screenSize,
    required this.padding,
    required this.devicePixelRatio,
    required this.isXSScreen,
    required this.isSmallScreen,
    required this.isVerySmallScreen,
    required this.isLargeScreen,
    required this.isXLScreen,
    required this.isLandscape,
    required this.isTablet,
    required this.isMobile,
    required this.isDesktop,
    required this.deviceType,
    required this.screenCategory,
    required this.densityCategory,
  });

  // Basic dimensions
  double get width => screenSize.width;
  double get height => screenSize.height;
  double get aspectRatio => width / height;
  double get diagonal => (width * width + height * height) / (width + height);

  // Enhanced responsive padding with more granular control
  double get horizontalPadding {
    switch (deviceType) {
      case DeviceType.mobileXS:
        return 12.0;
      case DeviceType.mobileSmall:
        return 16.0;
      case DeviceType.mobileMedium:
      case DeviceType.mobileLarge:
        return 20.0;
      case DeviceType.mobileXL:
        return 24.0;
      case DeviceType.tabletSmall:
        return 32.0;
      case DeviceType.tablet:
        return 40.0;
      case DeviceType.tabletLarge:
        return 48.0;
      case DeviceType.desktop:
        return 64.0;
      case DeviceType.desktopLarge:
        return 80.0;
    }
  }

  double get verticalPadding {
    if (isXSScreen) return 8.0;
    if (isVerySmallScreen) return 12.0;
    if (isSmallScreen) return 16.0;
    if (isLargeScreen) return 24.0;
    if (isXLScreen) return 32.0;
    return 20.0; // default
  }

  // Grid system support
  int get gridColumns {
    switch (screenCategory) {
      case ScreenCategory.mobile:
        return isLandscape ? 8 : 4;
      case ScreenCategory.tablet:
        return isLandscape ? 16 : 12;
      case ScreenCategory.desktop:
        return 24;
    }
  }

  // Responsive breakpoint helpers
  bool get isMobileXS => deviceType == DeviceType.mobileXS;
  bool get isMobileSmall => deviceType == DeviceType.mobileSmall;
  bool get isMobileMedium => deviceType == DeviceType.mobileMedium;
  bool get isMobileLarge => deviceType == DeviceType.mobileLarge;
  bool get isMobileXL => deviceType == DeviceType.mobileXL;
  bool get isTabletSmall => deviceType == DeviceType.tabletSmall;
  bool get isTabletLarge => deviceType == DeviceType.tabletLarge;
  bool get isDesktopLarge => deviceType == DeviceType.desktopLarge;

  // Density helpers
  bool get isLowDensity => densityCategory == DensityCategory.low;
  bool get isMediumDensity => densityCategory == DensityCategory.medium;
  bool get isHighDensity => densityCategory == DensityCategory.high;
}

/// Responsive spacing data class - RENAMED
class ResponsiveSpacingData {
  final ResponsiveDimensions dimensions;
  final double xs;
  final double sm;
  final double md;
  final double lg;
  final double xl;

  const ResponsiveSpacingData({
    required this.dimensions,
    required this.xs,
    required this.sm,
    required this.md,
    required this.lg,
    required this.xl,
  });
}

/// Responsive typography data class - RENAMED
class ResponsiveTypographyData {
  final ResponsiveDimensions dimensions;
  final double h1;
  final double h2;
  final double h3;
  final double h4;
  final double bodyLarge;
  final double bodyMedium;
  final double bodySmall;
  final double caption;

  const ResponsiveTypographyData({
    required this.dimensions,
    required this.h1,
    required this.h2,
    required this.h3,
    required this.h4,
    required this.bodyLarge,
    required this.bodyMedium,
    required this.bodySmall,
    required this.caption,
  });
}

/// Responsive button data class - RENAMED
class ResponsiveButtonData {
  final ResponsiveDimensions dimensions;
  final double heightLarge;
  final double heightMedium;
  final double heightSmall;
  final double borderRadius;
  final double fontSize;

  const ResponsiveButtonData({
    required this.dimensions,
    required this.heightLarge,
    required this.heightMedium,
    required this.heightSmall,
    required this.borderRadius,
    required this.fontSize,
  });
}

/// Keep this class as is for the button widget
class ResponsiveButtonDimensions {
  final double heightSmall;
  final double heightMedium;
  final double heightLarge;
  final double fontSize;
  final double borderRadius;

  const ResponsiveButtonDimensions({
    required this.heightSmall,
    required this.heightMedium,
    required this.heightLarge,
    required this.fontSize,
    required this.borderRadius,
  });
}

/// Responsive image data class - RENAMED
class ResponsiveImageData {
  final ResponsiveDimensions dimensions;
  final double large;
  final double medium;
  final double small;

  const ResponsiveImageData({
    required this.dimensions,
    required this.large,
    required this.medium,
    required this.small,
  });
}

/// Enhanced device type enumeration
enum DeviceType {
  mobileXS,      // 320px - Very small phones
  mobileSmall,   // 360px - Small phones
  mobileMedium,  // 375px - Medium phones (iPhone SE)
  mobileLarge,   // 414px - Large phones (iPhone Pro)
  mobileXL,      // 480px - Extra large phones
  tabletSmall,   // 600px - Small tablets
  tablet,        // 768px - Standard tablets
  tabletLarge,   // 1024px - Large tablets
  desktop,       // 1200px - Desktop
  desktopLarge,  // 1440px - Large desktop
}

/// Screen category for simplified logic
enum ScreenCategory {
  mobile,
  tablet,
  desktop,
}

/// Density category for high-DPI displays
enum DensityCategory {
  low,    // 1.0x
  medium, // 2.0x
  high,   // 3.0x+
}

/// Extension methods for easier usage - UPDATED
extension ResponsiveContext on BuildContext {
  ResponsiveDimensions get responsive => ResponsiveUtils.getDimensions(this);
  ResponsiveSpacingData get spacing => ResponsiveUtils.getSpacing(this);
  ResponsiveTypographyData get typography => ResponsiveUtils.getTypography(this);
  ResponsiveButtonData get button => ResponsiveUtils.getButton(this);
  ResponsiveImageData get image => ResponsiveUtils.getImage(this);
}

/// Responsive widget builder
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, ResponsiveDimensions dimensions) builder;

  const ResponsiveBuilder({
  super.key,
  required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return builder(context, context.responsive);
  }
}

/// Responsive layout widget for different screen sizes
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
  super.key,
  required this.mobile,
  this.tablet,
  this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    final dimensions = context.responsive;

    if (dimensions.deviceType == DeviceType.desktop && desktop != null) {
      return desktop!;
    }

    if (dimensions.isTablet && tablet != null) {
      return tablet!;
    }

    return mobile;
  }
}