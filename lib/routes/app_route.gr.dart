// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_route.dart';

abstract class _$AppRouter extends RootStackRouter {
  // ignore: unused_element
  _$AppRouter({super.navigatorKey});

  @override
  final Map<String, PageFactory> pagesMap = {
    EnhancedQubliHomeRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const EnhancedQubliHomeScreen(),
      );
    },
    HomeComparisonRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const HomeComparisonScreen(),
      );
    },
    LoginRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const LoginScreen(),
      );
    },
    QubliHomeRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const QubliHomeScreen(),
      );
    },
    RegisterRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const RegisterScreen(),
      );
    },
    SplashRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const SplashScreen(),
      );
    },
    UltraPerformanceHomeRoute.name: (routeData) {
      return AutoRoutePage<dynamic>(
        routeData: routeData,
        child: const UltraPerformanceHomeScreen(),
      );
    },
  };
}

/// generated route for
/// [EnhancedQubliHomeScreen]
class EnhancedQubliHomeRoute extends PageRouteInfo<void> {
  const EnhancedQubliHomeRoute({List<PageRouteInfo>? children})
      : super(
          EnhancedQubliHomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'EnhancedQubliHomeRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [HomeComparisonScreen]
class HomeComparisonRoute extends PageRouteInfo<void> {
  const HomeComparisonRoute({List<PageRouteInfo>? children})
      : super(
          HomeComparisonRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeComparisonRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [LoginScreen]
class LoginRoute extends PageRouteInfo<void> {
  const LoginRoute({List<PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [QubliHomeScreen]
class QubliHomeRoute extends PageRouteInfo<void> {
  const QubliHomeRoute({List<PageRouteInfo>? children})
      : super(
          QubliHomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'QubliHomeRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [RegisterScreen]
class RegisterRoute extends PageRouteInfo<void> {
  const RegisterRoute({List<PageRouteInfo>? children})
      : super(
          RegisterRoute.name,
          initialChildren: children,
        );

  static const String name = 'RegisterRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [SplashScreen]
class SplashRoute extends PageRouteInfo<void> {
  const SplashRoute({List<PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}

/// generated route for
/// [UltraPerformanceHomeScreen]
class UltraPerformanceHomeRoute extends PageRouteInfo<void> {
  const UltraPerformanceHomeRoute({List<PageRouteInfo>? children})
      : super(
          UltraPerformanceHomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'UltraPerformanceHomeRoute';

  static const PageInfo<void> page = PageInfo<void>(name);
}
