import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';

import '../../../../lib/features/home/<USER>/providers/home_provider.dart';
import '../../../../lib/features/home/<USER>/providers/individual/balance_notifier.dart';
import '../../../../lib/features/home/<USER>/providers/individual/favorites_notifier.dart';
import '../../../../lib/features/home/<USER>/providers/individual/banners_notifier.dart';

/// Test to demonstrate independent loading behavior
void main() {
  group('Independent Loading Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('Balance loads independently without waiting for others', () async {
      // Arrange: Mock repository with different response times
      // Balance: 100ms, Favorites: 500ms, Banners: 1000ms
      
      // Act: Start loading all sections
      final balanceNotifier = container.read(individualBalanceNotifierProvider.notifier);
      final favoritesNotifier = container.read(individualFavoritesNotifierProvider.notifier);
      final bannersNotifier = container.read(individualBannersNotifierProvider.notifier);

      // Start all loading simultaneously
      final balanceLoading = balanceNotifier.loadBalance();
      final favoritesLoading = favoritesNotifier.loadFavorites();
      final bannersLoading = bannersNotifier.loadBanners();

      // Assert: Balance should be ready first (100ms)
      await Future.delayed(const Duration(milliseconds: 150));
      
      final balanceState = container.read(individualBalanceNotifierProvider);
      final favoritesState = container.read(individualFavoritesNotifierProvider);
      final bannersState = container.read(individualBannersNotifierProvider);

      // Balance should be loaded, others still loading
      expect(balanceState.isLoading, false);
      expect(balanceState.hasData, true);
      expect(favoritesState.isLoading, true);  // Still loading
      expect(bannersState.isLoading, true);   // Still loading

      // Wait for favorites (500ms total)
      await Future.delayed(const Duration(milliseconds: 400));
      
      final favoritesStateAfter = container.read(individualFavoritesNotifierProvider);
      final bannersStateAfter = container.read(individualBannersNotifierProvider);

      // Favorites should be loaded, banners still loading
      expect(favoritesStateAfter.isLoading, false);
      expect(favoritesStateAfter.hasData, true);
      expect(bannersStateAfter.isLoading, true);  // Still loading

      // Wait for all to complete
      await Future.wait([balanceLoading, favoritesLoading, bannersLoading]);
    });

    test('Each section can refresh independently', () async {
      // Arrange: All sections loaded
      await _loadAllSections(container);

      // Act: Refresh only balance
      final balanceNotifier = container.read(individualBalanceNotifierProvider.notifier);
      await balanceNotifier.refresh();

      // Assert: Only balance loading state changed
      final balanceState = container.read(individualBalanceNotifierProvider);
      final favoritesState = container.read(individualFavoritesNotifierProvider);
      final bannersState = container.read(individualBannersNotifierProvider);

      // Balance was refreshed (new lastUpdated time)
      expect(balanceState.lastUpdated, isNotNull);
      
      // Others were not affected
      expect(favoritesState.isLoading, false);
      expect(bannersState.isLoading, false);
    });

    test('Error in one section does not affect others', () async {
      // Arrange: Mock balance to fail, others to succeed
      
      // Act: Load all sections
      final balanceNotifier = container.read(individualBalanceNotifierProvider.notifier);
      final favoritesNotifier = container.read(individualFavoritesNotifierProvider.notifier);
      final bannersNotifier = container.read(individualBannersNotifierProvider.notifier);

      await Future.wait([
        balanceNotifier.loadBalance(),
        favoritesNotifier.loadFavorites(),
        bannersNotifier.loadBanners(),
      ]);

      // Assert: Balance has error, others are successful
      final balanceState = container.read(individualBalanceNotifierProvider);
      final favoritesState = container.read(individualFavoritesNotifierProvider);
      final bannersState = container.read(individualBannersNotifierProvider);

      expect(balanceState.hasError, true);
      expect(favoritesState.hasError, false);
      expect(bannersState.hasError, false);
      expect(favoritesState.hasData, true);
      expect(bannersState.hasData, true);
    });

    test('Auto-refresh works independently for each section', () async {
      // This test would verify that each section's auto-refresh timer
      // works independently without affecting other sections
      
      // Arrange: Mock timers and repository
      
      // Act: Wait for auto-refresh intervals
      
      // Assert: Each section refreshes on its own schedule
      
      // Note: This would require more complex mocking of Timer.periodic
      // and is more of an integration test
    });
  });
}

/// Helper function to load all sections
Future<void> _loadAllSections(ProviderContainer container) async {
  final balanceNotifier = container.read(individualBalanceNotifierProvider.notifier);
  final favoritesNotifier = container.read(individualFavoritesNotifierProvider.notifier);
  final bannersNotifier = container.read(individualBannersNotifierProvider.notifier);

  await Future.wait([
    balanceNotifier.loadBalance(),
    favoritesNotifier.loadFavorites(),
    bannersNotifier.loadBanners(),
  ]);
}

/// Mock classes would be defined here
/// class MockHomeRepository extends Mock implements HomeRepository {}
